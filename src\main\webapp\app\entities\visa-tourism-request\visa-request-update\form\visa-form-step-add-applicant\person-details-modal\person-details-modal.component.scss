// Person Details Modal Styles
.modal-xl {
  max-width: 1200px;
}

// Verification step styling
.verification-step {
  .verification-icon {
    i {
      color: #0d6efd;
      opacity: 0.8;
    }
  }

  h4 {
    color: #0f2b2b;
    font-weight: 600;
  }

  .form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;

    .text-muted {
      font-weight: 400;
      font-size: 0.9em;
    }

    &.required::after {
      content: ' *';
      color: #dc3545;
    }
  }

  .form-control,
  .form-select {
    border: 1px solid #ced4da;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.3s ease;

    &:focus {
      border-color: #0d6efd;
      box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    &::placeholder {
      color: #6c757d;
      opacity: 0.7;
    }
  }

  .alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    border-radius: 8px;

    .alert-heading {
      color: #0c5460;
      font-weight: 600;
      margin-bottom: 12px;
    }

    ul {
      margin-bottom: 0;

      li {
        padding: 4px 0;

        i {
          width: 16px;
        }
      }
    }
  }
}

.progress-container {
  .progress {
    border-radius: 10px;
    background-color: #e9ecef;

    .progress-bar {
      border-radius: 10px;
      transition: width 0.3s ease;
    }
  }
}

.step-indicators {
  .step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      top: 15px;
      left: 50%;
      width: 100%;
      height: 2px;
      background-color: #dee2e6;
      z-index: 0;
    }

    &.completed:not(:last-child)::after {
      background-color: #198754;
    }

    .step-circle {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #dee2e6;
      color: #6c757d;
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 8px;
      position: relative;
      z-index: 1;
      transition: all 0.3s ease;
    }

    &.active .step-circle {
      background-color: #0d6efd;
      color: white;
    }

    &.completed .step-circle {
      background-color: #198754;
      color: white;
    }

    .step-title {
      font-size: 12px;
      text-align: center;
      color: #6c757d;
      font-weight: 500;
      max-width: 80px;
      line-height: 1.2;
    }

    &.active .step-title {
      color: #0d6efd;
      font-weight: 600;
    }

    &.completed .step-title {
      color: #198754;
    }

    &:hover {
      .step-circle {
        transform: scale(1.1);
      }
    }
  }
}

.step-content {
  min-height: 300px;

  .step-heading {
    color: #0f2b2b;
    font-weight: 600;
    font-size: 1.25rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
  }

  .section-subtitle {
    color: #495057;
    font-weight: 600;
    font-size: 1.1rem;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 8px;
    margin-bottom: 16px;
  }
}

.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;

  &.required::after {
    content: ' *';
    color: #dc3545;
  }
}

.form-control,
.form-select {
  border: 1px solid #ced4da;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.3s ease;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  &.is-invalid {
    border-color: #dc3545;

    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }
}

.text-danger.small {
  font-size: 12px;
  margin-top: 4px;
}

.btn {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-1px);
  }

  &:disabled {
    transform: none;
  }
}

.btn-primary {
  text-align: center;
}

.btn-success {
  background-color: #198754;
  border-color: #198754;
  color: #ffffff;

  &:hover:not(:disabled) {
    background-color: #157347;
    border-color: #146c43;
  }
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;

  &:hover:not(:disabled) {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
  }
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;

  &:hover:not(:disabled) {
    background-color: #5c636a;
    border-color: #565e64;
  }
}

// Responsive adjustments
@media (max-width: 991.98px) {
  .step-indicators {
    display: none !important;
  }

  .modal-xl {
    max-width: 95%;
  }

  .step-content {
    min-height: 250px;
  }
}

@media (max-width: 767.98px) {
  .modal-footer {
    .d-flex {
      flex-direction: column;
      gap: 10px;

      .btn {
        width: 100%;
      }
    }
  }

  .row.g-3 {
    .col-md-6 {
      margin-bottom: 1rem;
    }
  }
}

// Animation for step transitions
.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Custom checkbox and radio styles
.form-check-input {
  border-radius: 4px;
  border: 2px solid #ced4da;

  &:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
}

// Modal backdrop customization
.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.6);
}

// Close button styling
.btn-close {
  font-size: 1.2rem;
  opacity: 0.7;

  &:hover {
    opacity: 1;
  }
}

// Review summary styling
.review-summary {
  .card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 1rem;

    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      padding: 12px 16px;

      h6 {
        color: #495057;
        font-weight: 600;
      }
    }

    .card-body {
      padding: 16px;

      p {
        margin-bottom: 8px;
        font-size: 14px;

        strong {
          color: #495057;
          font-weight: 600;
        }
      }

      p:last-child {
        margin-bottom: 0;
      }
    }
  }

  .alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
    border-radius: 8px;

    .alert-heading {
      color: #0c5460;
      font-weight: 600;
    }
  }
}

// Form validation styling
.form-control.is-invalid,
.form-select.is-invalid {
  border-color: #dc3545;

  &:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
  }
}

.form-control.is-valid,
.form-select.is-valid {
  border-color: #198754;

  &:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
  }
}

// Checkbox and radio button styling
.form-check {
  margin-bottom: 1rem;

  .form-check-input {
    margin-top: 0.25rem;

    &:checked {
      background-color: #0d6efd;
      border-color: #0d6efd;
    }

    &:focus {
      border-color: #86b7fe;
      box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    // Radio button specific styling
    &[type='radio'] {
      border-radius: 50%;

      &:checked {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
      }
    }
  }

  .form-check-label {
    font-size: 14px;
    color: #495057;
    cursor: pointer;
    font-weight: 500;

    &:hover {
      color: #0d6efd;
    }
  }
}

// Loading state
.loading {
  opacity: 0.6;
  pointer-events: none;
}

// Success state
.success-message {
  color: #198754;
  font-weight: 500;
}

// Error state
.error-message {
  color: #dc3545;
  font-weight: 500;
}

// File upload styling
.file-upload-container {
  display: flex;
  align-items: center;
  gap: 12px;

  .file-upload-btn {
    background-color: #000000 !important;
    border-color: #000000 !important;
    color: #ffffff !important;
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background-color: #333333 !important;
      border-color: #333333 !important;
      transform: translateY(-1px);
    }

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(0, 0, 0, 0.25);
    }

    .text-white {
      color: #ffffff !important;
    }

    i {
      color: #ffffff !important;
    }
  }

  .file-name {
    color: #495057;
    font-size: 14px;
    font-weight: 500;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// Arabic text styling
[dir='rtl'] {
  text-align: right;

  .form-control {
    text-align: right;
  }
}

// Custom File Upload Dropzone Styles
.custom-file-dropzone {
  position: relative;
  border: 2px dashed #0f212b;
  border-radius: 20px;
  background-color: #ffffff;
  transition: all 0.3s ease;
  min-height: 160px;

  &:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
  }

  &.has-file {
    border-color: #28a745;
    background-color: #f8fff9;
  }

  &.error {
    border-color: #dc3545;
    background-color: #fff5f5;
  }
}

.file-input-hidden {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 2;
}

.file-dropzone-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 160px;
  cursor: pointer;
  margin: 0;
  padding: 1rem;

  .dropzone-content {
    text-align: center;
    color: #6c757d;

    .dropzone-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: #0f212b;
    }

    .dropzone-text {
      .main-text {
        display: block;
        font-size: 1rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
      }

      .sub-text {
        display: block;
        font-size: 0.875rem;
        color: #6c757d;
      }
    }
  }
}

.file-preview {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 3;

  .file-info {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 0.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .file-name {
      font-size: 0.875rem;
      color: #495057;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .btn {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
    }
  }
}

// RTL Support for file dropzone
[dir='rtl'] {
  .file-preview {
    right: auto;
    left: 10px;
  }
}
