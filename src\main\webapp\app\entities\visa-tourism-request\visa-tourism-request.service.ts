import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { VisaRequestSearchCriteria } from './visa-request-search-criteria.model';
import { PagedResponse } from './PagedResponse';
import { VisaRequestSummary } from './visa-request-summary.model';
import { ApplicationConfigService } from '../../core/config/application-config.service';
import {IYachtCharterPermit, NewYachtCharterPermit} from "../yacht-charter-permit/yacht-charter-permit.model";
import dayjs from "dayjs/esm";
import {IVisaTourismRequestModel, NewVisaTourismRequestModel} from "./visa-tourism-request.model";

type RestOf<T extends IVisaTourismRequestModel | NewVisaTourismRequestModel> = Omit<T, 'submitDate' > & {
  submitDate?: string | null;
};

export type RestVisaTourismRequest = RestOf<IVisaTourismRequestModel>;

export type NewRestVisaTourismRequest = RestOf<NewVisaTourismRequestModel>;

export type EntityResponseType = HttpResponse<IVisaTourismRequestModel>;
export type EntityArrayResponseType = HttpResponse<NewVisaTourismRequestModel[]>;

@Injectable({ providedIn: 'root' })
export class VisaRequestService {


  protected http = inject(HttpClient);
  protected applicationConfigService = inject(ApplicationConfigService);

  protected resourceUrl = this.applicationConfigService.getEndpointFor('/api/visa-requests');

  searchRequests(criteria: VisaRequestSearchCriteria, page: number = 0, size: number = 10): Observable<PagedResponse<VisaRequestSummary>> {
    const params = new HttpParams().set('page', page).set('size', size);

    return this.http.post<PagedResponse<VisaRequestSummary>>(`${this.resourceUrl}/search`, criteria, { params });
  }

  find(id: number): Observable<EntityResponseType> {
    return this.http
      .get<RestVisaTourismRequest>(`${this.resourceUrl}/${id}`, { observe: 'response' })
      .pipe(map(res => this.convertResponseFromServer(res)));
  }
  protected convertResponseFromServer(res: HttpResponse<RestVisaTourismRequest>): HttpResponse<IVisaTourismRequestModel> {
    return res.clone({
      body: res.body ? this.convertDateFromServer(res.body) : null,
    });
  }
  protected convertDateFromServer(restVisaTourismRequest: RestVisaTourismRequest): IVisaTourismRequestModel {
    return {
      ...restVisaTourismRequest,
      submitDate: restVisaTourismRequest.submitDate ? dayjs(restVisaTourismRequest.submitDate) : undefined,
    };
  }

  exportPdf(permitRequestId: number | undefined): Observable<ArrayBuffer> {
    const resourceUrl = this.resourceUrl + '/export-pdf';
    return this.http.get(`${resourceUrl}/${permitRequestId}`, { responseType: 'arraybuffer' });
  }

}
