{"srsaApp": {"visaTourism": {"establishmentDetails": "تفاصيل المنشأة", "home": {"title": "Toursim Visa Requests ", "refreshListLabel": "Resfresh", "createLabel": "Create a new Tourism Visa", "createOrEditLabel": "Edit", "notFound": "Not Founded"}, "created": "تم إنشاء تأشيرات سياحية ل {{ param }}", "updated": "تم تعديل تأشيرات سياحية ل {{ param }}", "deleted": "تم حذف تأشيرات سياحية ل {{ param }}", "delete": {"question": "هل متأكد من حذف التصريح الفني ل {{ id }}?"}, "detail": {"title": "Tourism Visa Requests"}, "status": {"NEW": "جديد", "DRAFT": "مسودّة", "DELETED": "م<PERSON><PERSON><PERSON><PERSON>", "ACTIVE": "فعال", "EXPIRED": "منتهي", "RETURNED": "معاد", "COMPLETED": "مكتمل", "CANCELLED": "ملغي", "null": "", "PENDING_REVIEW": "بإنتظار المراجعة", "PENDING_REPORT_APPROVAL": "تحت إعتماد التقرير", "PENDING_MANAGEMENT_LICENSING": "تحت الإعتماد", "RETURNED_INSPECTION_OFFICER": "معاد من قبل ضابط الفحص", "UNDER_PROCESS": "تحت المراجعة", "PENDING_INSPECTION": "بإنتظار المعاينة", "UNDER_INSPECTION": "تحت المعاينة", "PENDING_VISIT": "بانتظار الفحص المكتبي", "REJECTED": "مرفو<PERSON>", "VISIT_COMPLETED": "إتمام الزيارة الميدانية", "PENDING_PAYMENT": "بإنتظار الدفع", "RETURNED_LICENSING_OFFICER": "معاد من قبل إخصائي أول تراخيص السياحة الساحلية", "RETURNED_LICENSING_MANAGER": "معاد من قبل مدير إدارة تراخيص السياحة الساحلية"}, "permitPurpose": {"TOURISM": "السياحة", "REFUELING": "التزود بالوقود", "MAINTENANCE": "الصيانة", "STOCK_UP": "المخزون", "PARTICIPATION_IN_MARINE_EXHIBITIONS": "المشاركة في المعارض البحرية"}, "id": "الرقم", "marineOwnerNameAr": "إسم مالك الواسطة", "marineOwnerNameEn": "إسم مالك الواسطة", "marineOwnerIdNumber": "رقم هوية مالك الواسطة", "marineOwnerPassportNumber": "رقم جواز مالك الواسطة", "delegatorNameAr": "إسم الوكيل", "delegatorNameEn": "إسم الوكيل", "delegatorMobileNumber": "رقم جوال الوكيل", "delegatorEmailNumber": "البريد الإلكتروني للوكيل", "permitNumber": "رق<PERSON> الطلب", "requestStatus": "حالة الطلب", "marineColor": "لون الواسطة", "engineType": "نوع المحرك", "permitActivityLocation": "موقع الأنشطة", "permitActivityLatitude": "Permit Activity Latitude", "permitActivityLogitude": "Permit Activity Logitude", "noOfPassengers": "<PERSON><PERSON><PERSON> الركاب", "noOfCrew": "<PERSON><PERSON><PERSON> الطاقم", "captainName": "إس<PERSON> القبطان", "captainMobileNumber": "رقم جوال القبطان", "captainPassportNumber": "رقم جواز سفر القبطان", "arrivalDate": "تاريخ الوصول", "departureDate": "تاريخ المغادرة", "disclosureChecked": "Disclosure Checked", "passengers": "الركاب", "marineMedium": "الواسطة", "marineCompanyOwner": "الوكيل", "marineOwner": "المالك", "marineDelegatorUser": "الوكيل", "marineDelegatorCompany": "Marine Delegator Company", "newRequests": " الطلبات الجديدة", "select": "إختيار", "returnRq": "إعادة الطلب لإستكمال المتطلبات", "approveRq": "قبول", "returnRqConfirm": "هل انت متاكد من إعادة الطلب ؟", "returnRqReasonConfirm": "اسباب إعادة الطلب ", "confirmReturn": " إعادة الطلب ", "activity": "الأنشطه التي ستتم ممارستها", "otherActivity": "الأنشطه الأخرى", "Contract_Attachment": "عقد مبرم مع الوكيل الملاحي السياحي معتمد من الهيئة", "Document_yacht_Attachment": "مُستند يوّضح خط سير اليخت المخطط له ومدة إقامة اليخت ومناطق الإبحار داخل النطاق الجغرافي", "Yacht_Registration_Attachment": "شهادة تسجيل اليخت", "Copy_contract_Attachment": "نسخة من عقد الرصيف", "Crew_Data_Attachment": "قائمة بيانات افراد الطاقم وتاشيراته", "Copy_yacht_Attachment": "نسخة من رخصة اللاسلكي لليخت", "Insurance_Attachment": "وثيقة التامين", "Copy_passports_Attachment": "نسخة من جوازات السفر للطاقم والركاب", "Testimonies_Attachment": "شهادات الربان والطاقم المتواجدين على متن اليخت", "Passenger_Attachment": "قائمة بيانات الركاب", "General_Inspection_Attachment": "تقرير الفحص العام", "Certificate_Compliance_Attachment": "شهادة الامتثال الصادرة من دولة العلم", "Yacht_Owner_Identification_Attachment": "البيانات التعريفية لمالك اليخت", "allRequests": " All Requests", "needActionRequests": "Pending Requests", "draftRequests": "Draft Requests ", "navigationalData": "بيانات الترخيص الملاحي", "Yacht_Registration_Certificate": "شهادة تسجيل اليخت", "Copy_contract_sidewalk": "نسخة من عقد الرصيف", "Crew_Data_List_Visas": "قائمة بيانات افراد الطاقم وتاشيراته", "exceptionApproval": "هل الطلب معفي من الشروط", "feeRequired": "<PERSON><PERSON> يوجد رسوم للطلب", "noValidNavPermit": "Sorry, the operation cannot be completed due to the absence of a valid navigational license for the selected yacht .", "noValidTechnicalPermit": "Sorry, the operation cannot be completed due to the Active Visa Count For Passengers exceeded .", "noValidVisaPassengersCount": "Sorry, the operation cannot be completed due to the absence of a valid technical license for the selected yacht .", "visaModal": {"verification": "Verification", "welcome": "Welcome to Visa Application", "welcomeMessage": "Please ensure you have all required documents ready before proceeding with the application.", "verificationMessage": "Please provide your basic information to begin the visa application process.", "requiredDocuments": "Required Documents:", "importantNote": "Important Note", "verificationNote": "Please ensure the information matches exactly with your passport details. This information will be used throughout the application process.", "fullName": "Full Name", "passportNumber": "Passport Number", "nameRequired": "Full name is required", "passportRequired": "Passport number is required", "startApplication": "Verify", "addPersonDetails": "Add Person Details", "visaRequestView": "Visa Request View", "personalInformation": "Personal Information", "passportInformation": "Passport Information", "travelInformation": "Travel Information", "healthInformation": "Health Information", "emergencyContact": "Emergency Contact", "documentsUpload": "Documents Upload", "reviewSubmit": "Review & Submit", "fullNameEnglish": "Full Name (English)", "fullNameArabic": "Full Name (Arabic)", "firstNameArabic": "First Name (Arabic)", "fatherNameArabic": "Father Name (Arabic)", "grandfatherNameArabic": "Grandfather Name (Arabic)", "firstNameEnglish": "First Name (English)", "fatherNameEnglish": "Father Name (English)", "grandfatherNameEnglish": "Grandfather <PERSON> (English)", "documentType": "Document Type", "passportPhoto": "Passport Photo", "uploadPassportPhoto": "Upload Passport Photo", "uploadFile": "Upload File", "dragFilesHere": "Drag files here or click to upload", "fileUploadError": "File upload error", "arabicNames": "Arabic Names", "englishNames": "English Names", "personalDetails": "Personal Details", "firstNameArabicPlaceholder": "First Name", "fatherNameArabicPlaceholder": "Father Name", "grandfatherNameArabicPlaceholder": "Grandfather Name", "firstNameEnglishPlaceholder": "First Name", "fatherNameEnglishPlaceholder": "Father Name", "grandfatherNameEnglishPlaceholder": "Grandfather Name", "firstNameArabicRequired": "First name in Arabic is required", "fatherNameArabicRequired": "Father name in Arabic is required", "grandfatherNameArabicRequired": "Grandfather name in Arabic is required", "firstNameEnglishRequired": "First name in English is required", "fatherNameEnglishRequired": "Father name in English is required", "grandfatherNameEnglishRequired": "Grandfather name in English is required", "passportNumberPlaceholder": "Enter passport number", "nationality": "Nationality", "selectNationality": "Select Nationality", "nationalityRequired": "Nationality is required", "selectDocumentType": "Select Document Type", "documentTypeRequired": "Document type is required", "passport": "Passport", "nationalId": "National ID", "residencePermit": "Residence Permit", "selectPassportType": "Select Type", "passportTypeRequired": "Passport type is required", "ordinary": "Ordinary", "diplomatic": "Diplomatic", "service": "Service", "official": "Official", "birthDateRequired": "Birth date is required", "gender": "Gender", "selectGender": "Select Gender", "genderRequired": "Gender is required", "male": "Male", "female": "Female", "passportPhotoRequired": "Passport photo is required", "personalDetailsExtended": "Personal Details", "additionalInformation": "Additional Information", "locationInformation": "Location Information", "birthCountry": "Birth Country", "selectBirthCountry": "Select Birth Country", "birthCountryRequired": "Birth country is required", "residenceCountry": "Residence Country", "selectResidenceCountry": "Select Residence Country", "residenceCountryRequired": "Residence country is required", "birthPlace": "Birth Place", "birthPlacePlaceholder": "Enter birth place", "birthPlaceRequired": "Birth place is required", "countryCode": "Country Code", "countryCodePlaceholder": "Enter country code", "countryCodeRequired": "Country code is required", "passportDetails": "Passport Details", "contactInformation": "Contact Information", "additionalNationality": "Additional Nationality", "hasOtherNationalityQuestion": "Do you have another nationality?", "guardianInformation": "Guardian Information", "guardianName": "Guardian Name", "guardianRelation": "Relationship with Guardian", "guardianPassportNumber": "Guardian Passport Number", "guardianAge": "Guardian Age", "yes": "Yes", "no": "No", "father": "Father", "mother": "Mother", "brother": "Brother", "sister": "Sister", "uncle": "Uncle", "aunt": "Aunt", "grandfather": "Grandfather", "grandmother": "Grandmother", "birthDate": "Birth Date", "maritalStatus": "Marital Status", "passportIssueDate": "Passport Issue Date", "passportExpiryDate": "Passport Expiry Date", "passportCountry": "Passport Issuing Country", "email": "Email Address", "phoneNumber": "Phone Number", "alternatePhone": "Alternate Phone", "city": "City", "country": "Country", "postalCode": "Postal Code", "expectedExitDate": "Expected Exit Date", "purposeOfVisit": "<PERSON>ur<PERSON> of <PERSON><PERSON><PERSON>", "hasOtherNationality": "Do you have another nationality?", "otherNationality": "Other Nationality", "previousVisaToSaudi": "Have you previously visited Saudi Arabia?", "previousVisaDetails": "Previous Visa Details", "tookVaccines": "Have you taken required vaccinations?", "vaccineDetails": "Vaccine Details", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone", "emergencyContactRelation": "Relationship", "emergencyContactAddress": "Emergency Contact Address", "documentsUploaded": "Documents Uploaded", "cancel": "Cancel", "previous": "Previous", "next": "Next", "submit": "Submit", "submitting": "Submitting...", "selectMaritalStatus": "Select Marital Status", "family": "Family Visit", "medical": "Medical", "education": "Education", "other": "Other", "passportType": "Passport Type", "additionalDetails": "Additional Details", "personalStatus": "Personal Status", "maritalStatusRequired": "Marital status is required", "religion": "Religion", "selectReligion": "Select Religion", "religionRequired": "Religion is required", "islam": "Islam", "christianity": "Christianity", "judaism": "Judaism", "hinduism": "Hinduism", "buddhism": "Buddhism", "addressInformation": "Address Information", "address": "Address", "addressPlaceholder": "Enter your full address", "addressRequired": "Address is required", "workInformation": "Work Information", "occupation": "Nature of Work", "selectOccupation": "Select Occupation", "occupationRequired": "Nature of work is required", "engineer": "Engineer", "doctor": "Doctor", "teacher": "Teacher", "businessman": "Businessman", "student": "Student", "retired": "Retired", "unemployed": "Unemployed", "visaInformation": "Visa Information", "visaType": "Visa Type", "selectVisaType": "Select Visa Type", "visaTypeRequired": "Visa type is required", "selectVisaDuration": "Select Visa Duration", "visaDurationRequired": "Visa duration is required", "visaValidity": "Visa Validity", "selectVisaValidity": "Select Visa Validity", "visaValidityRequired": "Visa validity is required", "expectedEntryDate": "Expected Entry Date", "expectedEntryDateRequired": "Expected entry date is required", "personalPhoto": "Personal Photo", "uploadPersonalPhoto": "Upload Personal Photo", "personalPhotoRequired": "Personal photo is required", "travelHistory": "Travel History", "hasPreviousTravelQuestion": "Do you have previous travel history?", "previousTravelRequired": "Please select if you have previous travel history", "travelDetails": "Travel Details", "previousCountries": "Previous Travel Countries", "selectPreviousCountries": "Select Previous Countries", "previousCountriesRequired": "Previous travel countries are required", "travelPurpose": "Purpose of Travel", "selectTravelPurpose": "Select Purpose", "travelPurposeRequired": "Purpose of travel is required", "work": "Work", "religious": "Religious", "healthInsuranceQuestions": "Health and Insurance Questions", "disabilityInformation": "Disability Information", "hasDisabilityFromAccident": "Have you been in an accident that caused you a disability?", "medicalTreatment": "Medical Treatment", "currentlyHospitalized": "Are you currently hospitalized or receiving emergency treatment?", "congenitalConditions": "Congenital Conditions", "hasCongenitalCondition": "Do you suffer from any weakness or congenital deformity?", "pregnancyInformation": "Pregnancy Information", "currentlyPregnant": "Are you currently pregnant?", "pregnancyFromAssistance": "Is the current pregnancy a result of assisted reproductive methods?", "pregnancyMonth": "Which month of pregnancy?", "selectPregnancyMonth": "Select Month", "month1": "1st Month", "month2": "2nd Month", "month3": "3rd Month", "month4": "4th Month", "month5": "5th Month", "month6": "6th Month", "month7": "7th Month", "month8": "8th Month", "month9": "9th Month", "legalQuestions": "Legal Questions", "hasInterpolWarrant": "Has an arrest warrant been issued against the person by the International Police (Interpol)?", "hasBeenDeported": "Has the person been previously deported or expelled from any country, including violation of residence regulations?", "hasPassportRestrictions": "Does the person's passport contain any restrictions, conditions, or notes valid for one trip only?", "financialQuestions": "Financial Questions", "hasFinancialArrest": "Has the person been arrested due to financial issues?", "clarifications": "Clarifications", "clarificationsPlaceholder": "Please provide clarifications", "hasDisability": "Does the person have any disability?", "commitToProvideVaccinations": "Do you commit to providing original vaccinations when needed?", "numberOfEntries": "Number of Entries", "selectNumberOfEntries": "Select Number of Entries", "numberOfEntriesRequired": "Number of entries is required", "visaDurationMinError": "Duration must be at least 1 day", "personList": {"title": "Added Persons", "emptyState": "No persons added yet", "emptyStateDescription": "Click 'Add Person' to add travelers to this visa application", "mobileNumber": "Mobile Number", "passportNumber": "Passport Number", "name": "Name", "nationality": "Nationality", "gender": "Gender", "totalPersons": "Total Persons:"}, "employmentInformation": "Employment Information", "employmentStatus": "Employment Status", "currentEmploymentStatus": "Current Employment Status", "selectEmploymentStatus": "Select Employment Status", "employmentStatusRequired": "Employment status is required", "employed": "Employed", "selfEmployed": "Self-Employed", "housewife": "Housewife", "jobTitle": "Job Title", "jobTitlePlaceholder": "Enter job title", "jobTitleRequired": "Job title is required", "companyInformation": "Company Information", "employerName": "Employer/Company Name", "employerNamePlaceholder": "Enter employer/company name", "employerNameRequired": "Employer name is required", "workExperience": "Years of Work Experience", "selectWorkExperience": "Select Experience", "workExperienceRequired": "Work experience is required", "experience0to1": "0-1 Years", "experience2to5": "2-5 Years", "experience6to10": "6-10 Years", "experience11to15": "11-15 Years", "experience16to20": "16-20 Years", "experience20plus": "20+ Years", "employerAddress": "Employer Address", "employerAddressPlaceholder": "Enter complete employer address", "employerAddressRequired": "Employer address is required", "financialInformation": "Financial Information", "monthlyIncome": "Monthly Income", "selectMonthlyIncome": "Select Monthly Income", "monthlyIncomeRequired": "Monthly income is required", "incomeBelow1000": "Below $1,000", "income1000to3000": "$1,000 - $3,000", "income3000to5000": "$3,000 - $5,000", "income5000to10000": "$5,000 - $10,000", "incomeAbove10000": "Above $10,000", "employmentContract": "Employment Contract", "employmentContractNote": "Upload employment contract or salary certificate (Optional)", "arabicOnlyError": "This field should contain only Arabic characters", "englishOnlyError": "This field should contain only English characters", "arabicOrEnglishError": "This field should contain only Arabic or English characters", "visaDuration": "visa Duration", "crewType": "Crew Type", "crew": "Crew", "visitor": "Visitor", "selectCrewType": "Select Crew Type", "wantsUmrah": "wants <PERSON><PERSON> ?", "single": "Single", "married": "Married", "divorced": "Divorced", "widowed": "Widowed", "mobileNumber": "Mobile Number", "emailAddress": "Email Address", "legalFinancialQuestions": "Legal and Financial Questions"}}}}