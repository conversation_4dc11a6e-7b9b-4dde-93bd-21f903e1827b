import { Component, OnInit, Output, EventEmitter, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl, ValidationErrors } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import TranslateDirective from 'app/shared/language/translate.directive';
import { VisaApplicant } from '../../../../visa-applicant.model';
import { GregorianCalendarComponent } from 'app/shared/gregorian-calendar/gregorian-calendar.component';
import { DropzoneComponent, DropzoneConfigInterface, DropzoneModule } from 'ngx-dropzone-wrapper';

// Custom Validators
export class CustomValidators {
  static arabicOnly(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null; // Don't validate empty values, let required validator handle that
    }
    const arabicPattern = /^[\u0621-\u064A\u0660-\u0669\s]+$/;
    return arabicPattern.test(control.value) ? null : { arabicOnly: true };
  }

  static englishOnly(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null; // Don't validate empty values, let required validator handle that
    }
    const englishPattern = /^[a-zA-Z\s]+$/;
    return englishPattern.test(control.value) ? null : { englishOnly: true };
  }

  static arabicOrEnglish(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null; // Don't validate empty values, let required validator handle that
    }
    const arabicOrEnglishPattern = /^[a-zA-Z\u0621-\u064A\u0660-\u0669\s]+$/;
    return arabicOrEnglishPattern.test(control.value) ? null : { arabicOrEnglish: true };
  }
}

@Component({
  selector: 'jhi-person-details-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, TranslateModule, TranslateDirective, GregorianCalendarComponent, DropzoneModule],
  templateUrl: './person-details-modal.component.html',
  styleUrl: './person-details-modal.component.scss',
})
export class PersonDetailsModalComponent implements OnInit {
  @Output() personAdded = new EventEmitter<VisaApplicant>();
  @ViewChild('dropzonePassportPhoto', { static: false }) dropzonePassportPhoto!: DropzoneComponent;
  @ViewChild('dropzonePersonalPhoto', { static: false }) dropzonePersonalPhoto!: DropzoneComponent;

  currentStep = 0; // Start with verification page
  totalSteps = 10; // Actual form steps (after verification)
  personForm: FormGroup;
  isSubmitting = false;
  showTravelDetails = false;
  showPregnancyDetails = false;
  showFinancialArrestDetails = false;

  // File upload properties
  passportPhotoFile: File | null = null;
  personalPhotoFile: File | null = null;
  passportPhotoError = false;
  personalPhotoError = false;

  // File upload status indicators
  get hasPassportPhoto(): boolean {
    return !!(this.passportPhotoFile || this.personForm.get('passportPhoto')?.value);
  }

  get hasPersonalPhoto(): boolean {
    return !!(this.personalPhotoFile || this.personForm.get('personalPhoto')?.value);
  }

  get passportPhotoName(): string {
    const file = this.passportPhotoFile || this.personForm.get('passportPhoto')?.value;
    return file?.name || 'passport-photo.jpg';
  }

  get personalPhotoName(): string {
    const file = this.personalPhotoFile || this.personForm.get('personalPhoto')?.value;
    return file?.name || 'personal-photo.jpg';
  }

  // Step definitions based on Pop-up images
  steps = [
    { id: 1, title: 'Personal Information', titleAr: 'المعلومات الشخصية' },
    { id: 2, title: 'Passport Information', titleAr: 'معلومات جواز السفر' },
    { id: 3, title: 'Contact Information', titleAr: 'معلومات الاتصال' },
    { id: 4, title: 'Travel Information', titleAr: 'معلومات السفر' },
    { id: 5, title: 'Employment Information', titleAr: 'معلومات العمل' },
    { id: 6, title: 'Additional Details', titleAr: 'تفاصيل إضافية' },
    { id: 7, title: 'Health Information', titleAr: 'المعلومات الصحية' },
    { id: 8, title: 'Legal Questions', titleAr: 'الأسئلة القانونية' },
    { id: 9, title: 'Background Check', titleAr: 'فحص الخلفية' },
    { id: 10, title: 'Security Questions', titleAr: 'الأسئلة الأمنية' },
  ];

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
  ) {
    this.personForm = this.createForm();
  }

  ngOnInit(): void {
    // Initialize form if needed
    // Set up form value changes to sync fullNameEn with individual name fields
    this.setupFormValueChanges();

    // Restore initial UI state
    this.restoreStepUIState();
  }

  private setupFormValueChanges(): void {
    // Sync fullNameEn with individual English name fields
    const nameFields = ['firstNameEn', 'fatherNameEn', 'grandfatherNameEn'];
    nameFields.forEach(field => {
      this.personForm.get(field)?.valueChanges.subscribe(() => {
        this.updateFullNameEn();
      });
    });
  }

  private updateFullNameEn(): void {
    const firstNameEn = this.personForm.get('firstNameEn')?.value || '';
    const fatherNameEn = this.personForm.get('fatherNameEn')?.value || '';
    const grandfatherNameEn = this.personForm.get('grandfatherNameEn')?.value || '';
    const fullNameEn = `${firstNameEn} ${fatherNameEn} ${grandfatherNameEn}`.trim();

    this.personForm.get('fullNameEn')?.setValue(fullNameEn, { emitEvent: false });
  }

  private createForm(): FormGroup {
    return this.fb.group({
      // Step 1: Personal Information (Pop-up 1) - Updated fields
      firstNameAr: ['', [Validators.required, CustomValidators.arabicOnly]],
      fatherNameAr: ['', [Validators.required, CustomValidators.arabicOnly]],
      grandfatherNameAr: ['', [Validators.required, CustomValidators.arabicOnly]],
      firstNameEn: ['', [Validators.required, CustomValidators.englishOnly]],
      fatherNameEn: ['', [Validators.required, CustomValidators.englishOnly]],
      grandfatherNameEn: ['', [Validators.required, CustomValidators.englishOnly]],
      passportNumber: ['', Validators.required],
      nationality: ['', Validators.required],
      documentType: ['', Validators.required],
      passportType: ['', Validators.required],
      birthDate: ['', Validators.required],
      gender: ['', Validators.required],
      passportPhoto: ['', Validators.required],

      // Step 2: Additional Information (Pop-up 2) - All new fields
      birthCountry: ['', Validators.required],
      residenceCountry: ['', Validators.required],
      birthPlace: ['', [Validators.required, CustomValidators.arabicOrEnglish]],
      countryCode: ['', Validators.required],
      passportIssueDate: ['', Validators.required],
      passportExpiryDate: ['', Validators.required],
      passportCountry: ['', Validators.required],
      phoneNumber: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      hasOtherNationality: [''],
      guardianName: ['', CustomValidators.arabicOrEnglish],
      guardianRelation: ['', CustomValidators.arabicOrEnglish],
      guardianPassportNumber: [''],
      guardianAge: [''],
      // New fields for Step 2
      maritalStatus: ['', Validators.required],
      religion: ['', Validators.required],
      occupation: ['', [Validators.required, CustomValidators.arabicOrEnglish]],
      address: ['', [Validators.required, CustomValidators.arabicOrEnglish]],
      visaType: [''],
      numberOfEntries: [''],
      visaDuration: [''],
      visaValidity: [''],
      expectedEntryDate: [''],
      personalPhoto: [''],

      // Step 4: Travel History (Pop-up 4) - Travel history fields
      hasPreviousTravel: ['', Validators.required],
      previousCountries: [''],
      travelPurpose: [''],
      travelFromDate: [''],
      travelToDate: [''],

      // Step 5: Contact Information (Pop-up 5) - Additional contact fields
      alternatePhone: [''],
      city: [''],
      country: [''],
      postalCode: [''],

      // Step 6: Travel Information (Pop-up 6)
      expectedExitDate: [''],
      purposeOfVisit: [''],
      commitToProvideVaccinations: [''],
      hasInterpolWarrant: [''],
      hasBeenDeported: [''],
      hasPassportRestrictions: [''],

      // Step 6: Additional Details (Pop-up 6)
      otherNationality: [''],
      previousVisaToSaudi: [false],
      previousVisaDetails: [''],

      // Step 7: Health Information (Pop-up 7)
      hasDisability: [false],
      disabilityDetails: [''],
      tookVaccines: [false],
      vaccineDetails: [''],

      // Step 8: Legal Questions - هل تم القبض على الشخص في بلده؟
      hasBeenArrested: [''],

      // Step 9: Background Check - هل عمل الشخص فى المجال الاعلامي او المجال السياسي؟
      hasMediaPoliticalWork: [''],

      // Step 10: Security Questions - هل يننتمي الشخص لاى منظمة ارهابية ؟
      hasTerroristAffiliation: [''],

      // Additional fields for compatibility
      fullName: [''], // Will be computed from fullNameEn
      fullNameEn: ['', Validators.required], // Add missing fullNameEn field for verification step
      identityType: ['PASSPORT'],
      role: ['PASSENGER'],
      wantsUmrah: [false],
    });
  }

  nextStep(): void {
    if (this.currentStep === 0) {
      // Validate verification step before proceeding
      if (this.isCurrentStepValid()) {
        this.currentStep = 1;
      } else {
        // Mark fields as touched to show validation errors
        this.getCurrentStepFields().forEach(field => {
          this.personForm.get(field)?.markAsTouched();
        });
      }
    } else if (this.isCurrentStepValid() && this.currentStep < this.totalSteps) {
      this.currentStep++;
    }

    // Update fullNameEn when moving between steps to ensure it's always current
    this.updateFullNameEn();

    // Restore UI state for the new step
    this.restoreStepUIState();
  }

  previousStep(): void {
    if (this.currentStep > 0) {
      this.currentStep--;
      // Restore UI state for the previous step
      this.restoreStepUIState();
    }
  }

  goToStep(step: number): void {
    if (step >= 0 && step <= this.totalSteps) {
      this.currentStep = step;
      // Restore UI state for the target step
      this.restoreStepUIState();
    }
  }

  // Restore UI state when navigating between steps
  private restoreStepUIState(): void {
    // Use setTimeout to ensure the DOM is updated before restoring state
    setTimeout(() => {
      this.restoreFileUploadState();
      this.restoreConditionalFieldsState();
    }, 100);
  }

  // Restore file upload state
  private restoreFileUploadState(): void {
    // Restore passport photo state
    const passportPhotoValue = this.personForm.get('passportPhoto')?.value;
    if (passportPhotoValue) {
      this.passportPhotoFile = passportPhotoValue;
      this.passportPhotoError = false;
      // Note: We don't restore files to the dropzone to keep it visible and functional
      // The file reference is maintained in the form and component state
    }

    // Restore personal photo state
    const personalPhotoValue = this.personForm.get('personalPhoto')?.value;
    if (personalPhotoValue) {
      this.personalPhotoFile = personalPhotoValue;
      this.personalPhotoError = false;
      // Note: We don't restore files to the dropzone to keep it visible and functional
      // The file reference is maintained in the form and component state
    }
  }

  // Restore conditional fields state (like travel details, pregnancy details, etc.)
  private restoreConditionalFieldsState(): void {
    // Restore travel details visibility
    const hasPreviousTravel = this.personForm.get('hasPreviousTravel')?.value;
    if (hasPreviousTravel === 'true' || hasPreviousTravel === true) {
      this.showTravelDetails = true;
    }

    // Restore pregnancy details visibility (if this field exists)
    const isPregnant = this.personForm.get('isPregnant')?.value;
    if (isPregnant === 'true' || isPregnant === true) {
      this.showPregnancyDetails = true;
    }

    // Restore financial arrest details visibility (if this field exists)
    const hasFinancialArrest = this.personForm.get('hasFinancialArrest')?.value;
    if (hasFinancialArrest === 'true' || hasFinancialArrest === true) {
      this.showFinancialArrestDetails = true;
    }
  }

  isCurrentStepValid(): boolean {
    const currentStepFields = this.getCurrentStepFields();
    return currentStepFields.every(field => {
      const control = this.personForm.get(field);
      return control ? control.valid : true;
    });
  }

  private getCurrentStepFields(): string[] {
    switch (this.currentStep) {
      case 0:
        return ['fullNameEn', 'passportNumber', 'nationality']; // Verification page fields
      case 1:
        return [
          'firstNameAr',
          'fatherNameAr',
          'grandfatherNameAr',
          'firstNameEn',
          'fatherNameEn',
          'grandfatherNameEn',
          'passportNumber',
          'nationality',
          'documentType',
          'passportType',
          'birthDate',
          'gender',
          'passportPhoto',
        ];
      case 2:
        return [
          'birthCountry',
          'residenceCountry',
          'birthPlace',
          'countryCode',
          'passportIssueDate',
          'passportExpiryDate',
          'passportCountry',
          'phoneNumber',
          'email',
        ];
      case 3:
        return [
          'maritalStatus',
          'religion',
          'address',
          'occupation',
          'visaType',
          'numberOfEntries',
          'visaDuration',
          'visaValidity',
          'expectedEntryDate',
          'personalPhoto',
        ];
      case 4:
        const travelFields = ['hasPreviousTravel'];
        if (this.showTravelDetails) {
          travelFields.push('previousCountries', 'travelPurpose', 'travelFromDate', 'travelToDate');
        }
        return travelFields;
      case 5:
        return []; // No required fields for step 5
      case 6:
        return []; // No required fields for step 6
      case 7:
        return []; // No required fields for step 7
      case 8:
        return []; // No required fields for step 8
      case 9:
        return []; // Documents upload - handled separately
      case 10:
        return []; // Review page - no validation needed
      default:
        return [];
    }
  }

  getProgressPercentage(): number {
    if (this.currentStep === 0) return 0; // Verification page
    return (this.currentStep / this.totalSteps) * 100;
  }

  isVerificationStep(): boolean {
    return this.currentStep === 0;
  }

  isFormStep(): boolean {
    return this.currentStep > 0 && this.currentStep <= this.totalSteps;
  }

  isArabic(): boolean {
    return this.translateService.currentLang === 'ar';
  }

  // Debug method - can be called from browser console
  debugFormValidation(): void {
    console.log('=== MANUAL FORM DEBUG ===');
    console.log('Current step:', this.currentStep);
    console.log('Total steps:', this.totalSteps);
    console.log('Is final step (currentStep === totalSteps):', this.currentStep === this.totalSteps);
    console.log('Form valid for submission:', this.isFormValidForSubmission());
    console.log('Is submitting:', this.isSubmitting);
    console.log(
      'Submit button should be enabled:',
      this.currentStep === this.totalSteps && this.isFormValidForSubmission() && !this.isSubmitting,
    );
    console.log('Form value:', this.personForm.value);
    console.log('Form errors:', this.personForm.errors);
    console.log('Form status:', this.personForm.status);
  }

  // Check if all required fields for completed steps are valid
  isFormValidForSubmission(): boolean {
    // Get all required fields from all completed steps
    const allRequiredFields: string[] = [];

    for (let step = 0; step <= this.totalSteps; step++) {
      const stepFields = this.getCurrentStepFieldsForStep(step);
      allRequiredFields.push(...stepFields);
    }

    // Check if all required fields are valid
    const invalidFields: string[] = [];

    allRequiredFields.forEach(field => {
      const control = this.personForm.get(field);
      if (!control) {
        invalidFields.push(field);
        return;
      }

      const isValid = control.valid;
      const hasValue = control.value !== null && control.value !== undefined && control.value !== '';
      const isBooleanField = typeof control.value === 'boolean';

      if (!isValid || (!hasValue && !isBooleanField)) {
        invalidFields.push(field);
      }
    });

    // Only log if there are invalid fields (for debugging)
    if (invalidFields.length > 0) {
      console.log('Invalid fields preventing submission:', invalidFields);
    }

    return invalidFields.length === 0;
  }

  private getCurrentStepFieldsForStep(step: number): string[] {
    switch (step) {
      case 0:
        return ['fullNameEn', 'passportNumber', 'nationality']; // Verification page fields
      case 1:
        return [
          'firstNameAr',
          'fatherNameAr',
          'grandfatherNameAr',
          'firstNameEn',
          'fatherNameEn',
          'grandfatherNameEn',
          'passportNumber',
          'nationality',
          'documentType',
          'passportType',
          'birthDate',
          'gender',
          'passportPhoto',
        ];
      case 2:
        return [
          'birthCountry',
          'residenceCountry',
          'birthPlace',
          'countryCode',
          'passportIssueDate',
          'passportExpiryDate',
          'passportCountry',
          'phoneNumber',
          'email',
        ];
      case 3:
        return ['maritalStatus', 'religion', 'address', 'occupation'];
      case 4:
        const travelFields = ['hasPreviousTravel'];
        if (this.showTravelDetails) {
          travelFields.push('previousCountries', 'travelPurpose', 'travelFromDate', 'travelToDate');
        }
        return travelFields;
      case 5:
        return []; // No required fields for step 5
      case 6:
        return []; // No required fields for step 6
      case 7:
        return []; // No required fields for step 7
      case 8:
        return []; // No required fields for step 8
      case 9:
        return []; // Documents upload - handled separately
      case 10:
        return []; // Review page - no validation needed
      default:
        return [];
    }
  }

  // Dropzone configuration methods
  getDropzoneConfig(): DropzoneConfigInterface {
    return {
      clickable: true,
      addRemoveLinks: true,
      maxFilesize: 5,
      acceptedFiles: '.pdf,.jpeg,.jpg,.png',
      dictDefaultMessage: this.isArabic() ? 'اسحب الملفات هنا أو انقر للتحميل' : 'Drag files here or click to upload',
      dictFallbackMessage: this.isArabic() ? 'متصفحك لا يدعم تحميل الملفات' : 'Your browser does not support drag and drop file uploads',
      dictInvalidFileType: this.isArabic() ? 'نوع الملف غير مدعوم' : 'File type not supported',
      dictFileTooBig: this.isArabic() ? 'الحجم الأقصى للملف هو 5 MB' : 'Maximum file size is 5 MB',
      url: SERVER_API_URL + '/api/files',
    };
  }

  getPassportPhotoConfig(): DropzoneConfigInterface {
    return {
      ...this.getDropzoneConfig(),
      maxFiles: 1,
      acceptedFiles: '.jpeg,.jpg,.png',
      autoProcessQueue: false,
    };
  }

  getPersonalPhotoConfig(): DropzoneConfigInterface {
    return {
      ...this.getDropzoneConfig(),
      maxFiles: 1,
      acceptedFiles: '.jpeg,.jpg,.png',
      autoProcessQueue: false,
    };
  }

  // Passport photo upload handlers
  onPassportPhotoSuccess(event: any): void {
    // If there's already a file, remove it first to allow replacement
    const dz = this.dropzonePassportPhoto?.directiveRef?.dropzone();
    if (dz && dz.files.length > 1) {
      // Remove all files except the current one
      const filesToRemove = dz.files.slice(0, -1);
      filesToRemove.forEach((file: any) => dz.removeFile(file));
    }

    this.passportPhotoError = false;
    this.passportPhotoFile = event;
    this.personForm.patchValue({
      passportPhoto: event,
    });
    this.personForm.get('passportPhoto')?.markAsTouched();
  }

  onPassportPhotoError(event: any): void {
    this.passportPhotoError = true;
    this.passportPhotoFile = null;
  }

  onPassportPhotoRemoved(event: any): void {
    this.passportPhotoFile = null;
    this.passportPhotoError = false;
    this.personForm.patchValue({
      passportPhoto: null,
    });
    this.personForm.get('passportPhoto')?.markAsTouched();
  }

  // Personal photo upload handlers
  onPersonalPhotoSuccess(event: any): void {
    // If there's already a file, remove it first to allow replacement
    const dz = this.dropzonePersonalPhoto?.directiveRef?.dropzone();
    if (dz && dz.files.length > 1) {
      // Remove all files except the current one
      const filesToRemove = dz.files.slice(0, -1);
      filesToRemove.forEach((file: any) => dz.removeFile(file));
    }

    this.personalPhotoError = false;
    this.personalPhotoFile = event;
    this.personForm.patchValue({
      personalPhoto: event,
    });
    this.personForm.get('personalPhoto')?.markAsTouched();
  }

  onPersonalPhotoError(event: any): void {
    this.personalPhotoError = true;
    this.personalPhotoFile = null;
  }

  onPersonalPhotoRemoved(event: any): void {
    this.personalPhotoFile = null;
    this.personalPhotoError = false;
    this.personForm.patchValue({
      personalPhoto: null,
    });
    this.personForm.get('personalPhoto')?.markAsTouched();
  }

  // Pregnancy change handler
  onPregnancyChange(event: any): void {
    const isPregnant = event.target.value === 'true';
    this.showPregnancyDetails = isPregnant;

    if (!isPregnant) {
      // Clear pregnancy-related fields when "No" is selected
      this.personForm.patchValue({
        pregnancyFromAssistance: '',
        pregnancyMonth: '',
      });
    }
  }

  // Financial arrest change handler
  onFinancialArrestChange(event: any): void {
    const hasFinancialArrest = event.target.value === 'true';
    this.showFinancialArrestDetails = hasFinancialArrest;

    if (!hasFinancialArrest) {
      // Clear financial arrest clarifications when "No" is selected
      this.personForm.patchValue({
        financialArrestClarifications: '',
      });
    }
  }

  // Travel history methods
  onPreviousTravelChange(hasTravel: boolean): void {
    this.showTravelDetails = hasTravel;

    if (hasTravel) {
      // Add validators when travel details are required
      this.personForm.get('previousCountries')?.setValidators([Validators.required]);
      this.personForm.get('travelPurpose')?.setValidators([Validators.required]);
      this.personForm.get('travelFromDate')?.setValidators([Validators.required]);
      this.personForm.get('travelToDate')?.setValidators([Validators.required]);
    } else {
      // Remove validators and clear values when not needed
      this.personForm.get('previousCountries')?.clearValidators();
      this.personForm.get('travelPurpose')?.clearValidators();
      this.personForm.get('travelFromDate')?.clearValidators();
      this.personForm.get('travelToDate')?.clearValidators();

      this.personForm.patchValue({
        previousCountries: '',
        travelPurpose: '',
        travelFromDate: '',
        travelToDate: '',
      });
    }

    // Update validators
    this.personForm.get('previousCountries')?.updateValueAndValidity();
    this.personForm.get('travelPurpose')?.updateValueAndValidity();
    this.personForm.get('travelFromDate')?.updateValueAndValidity();
    this.personForm.get('travelToDate')?.updateValueAndValidity();
  }

  onSubmit(): void {
    if (this.isFormValidForSubmission() && !this.isSubmitting) {
      this.isSubmitting = true;

      // Combine English and Arabic names
      const firstNameEn = this.personForm.get('firstNameEn')?.value || '';
      const fatherNameEn = this.personForm.get('fatherNameEn')?.value || '';
      const grandfatherNameEn = this.personForm.get('grandfatherNameEn')?.value || '';
      const firstNameAr = this.personForm.get('firstNameAr')?.value || '';
      const fatherNameAr = this.personForm.get('fatherNameAr')?.value || '';
      const grandfatherNameAr = this.personForm.get('grandfatherNameAr')?.value || '';

      const fullNameEn = `${firstNameEn} ${fatherNameEn} ${grandfatherNameEn}`.trim();
      const fullNameAr = `${firstNameAr} ${fatherNameAr} ${grandfatherNameAr}`.trim();
      const combinedFullName = fullNameEn + (fullNameAr ? ` (${fullNameAr})` : '');

      const formData: VisaApplicant = {
        fullName: combinedFullName,
        passportNumber: this.personForm.get('passportNumber')?.value || '',
        nationality: this.personForm.get('nationality')?.value || '',
        birthDate: this.personForm.get('birthDate')?.value || '',
        gender: this.personForm.get('gender')?.value || '',
        identityType: this.personForm.get('identityType')?.value || 'PASSPORT',
        role: this.personForm.get('role')?.value || 'PASSENGER',
        jobTitle: this.personForm.get('occupation')?.value || '',
        maritalStatus: this.personForm.get('maritalStatus')?.value || '',
        religion: this.personForm.get('religion')?.value || '',
        email: this.personForm.get('email')?.value || '',
        phoneNumber: this.personForm.get('phoneNumber')?.value || '',
        address: this.personForm.get('address')?.value || '',
        passportIssueDate: this.personForm.get('passportIssueDate')?.value || '',
        passportExpiryDate: this.personForm.get('passportExpiryDate')?.value || '',
        passportCountry: this.personForm.get('passportCountry')?.value || '',
        birthCountry: this.personForm.get('birthPlace')?.value || '',
        residenceCountry: this.personForm.get('country')?.value || '',
        expectedEntryDate: this.personForm.get('expectedEntryDate')?.value || '',
        numberOfEntries: this.personForm.get('numberOfEntries')?.value || '',
        visaDurationDays: this.personForm.get('visaDurationDays')?.value || 0,
        wantsUmrah: this.personForm.get('wantsUmrah')?.value || false,
        hasOtherNationality: this.personForm.get('hasOtherNationality')?.value || false,
        otherNationality: this.personForm.get('otherNationality')?.value || undefined,
        hasDisability: this.personForm.get('hasDisability')?.value || false,
        tookVaccines: this.personForm.get('tookVaccines')?.value || false,
      };

      console.log('Form submitted:', formData);

      // Emit the person data to parent component
      this.personAdded.emit(formData);

      // Simulate API call delay
      setTimeout(() => {
        this.isSubmitting = false;
        this.closeModal();
      }, 1000);
    }
  }

  closeModal(): void {
    // Reset form and step
    this.currentStep = 0; // Reset to verification page
    this.personForm.reset();
    this.isSubmitting = false;

    // Close Bootstrap modal
    const modalElement = document.getElementById('personDetailsModal');
    if (modalElement) {
      const modal = (window as any).bootstrap.Modal.getInstance(modalElement);
      if (modal) {
        modal.hide();
      }
    }
  }

  isStepCompleted(step: number): boolean {
    if (step < this.currentStep) {
      const stepFields = this.getStepFields(step);
      return stepFields.every(field => {
        const control = this.personForm.get(field);
        return control ? control.valid && control.value : false;
      });
    }
    return false;
  }

  private getStepFields(step: number): string[] {
    switch (step) {
      case 1:
        return ['fullName', 'birthDate', 'gender', 'maritalStatus'];
      case 2:
        return ['identityType', 'nationality', 'birthCountry', 'residenceCountry'];
      case 3:
        return ['passportNumber', 'passportIssueDate', 'passportExpiryDate', 'passportCountry'];
      case 4:
        return ['email', 'phoneNumber'];
      case 5:
        return []; // No required fields for step 5
      case 6:
        return []; // No required fields for step 6
      case 7:
        return []; // No required fields for step 7
      case 8:
        return []; // No required fields for step 8
      case 9:
        return []; // No required fields for step 9
      case 10:
        return []; // No required fields for step 10
      default:
        return [];
    }
  }
}
