<div class="logo-Ebhar-sub min-vh-100">
  <div class="mk-permit-container mk-inner-layout-container">
    <div class="card p-4">
      <div class="mk-inner-layout-content">
        <div id="page-heading" data-cy="PermitRequestHeading" class="d-flex-between gap-4 mb-4">
          <h2 class="d-flex gap-2 m-0">
            <span
              id="user-management-page-heading"
              jhiTranslate="srsaApp.visaTourism.home.title"
              data-cy="userManagementPageHeading"
              class="fs-4 fw-bold"
              >إدارة الطلبات</span
            >
          </h2>

          <div class="d-flex justify-content-end w-sm-100" *jhiHasAnyAuthority="['ROLE_SERVICE_PROVIDER', 'ROLE_INDIVIDUAL_USER']">
            <button
              id="jh-create-entity"
              data-cy="entityCreateButton"
              class="btn btn-primary px-4 w-sm-100"
              [routerLink]="['/visa-tourism/new']"
            >
              <fa-icon icon="plus"></fa-icon>
              <span jhiTranslate="srsaApp.visaTourism.home.createLabel">Create a new Tourism Visa</span>
            </button>
          </div>
        </div>

        <div class="d-flex g-4 mb-4 justify-content-between align-items-end">
          <div class="dga-content-switcher-header">
            <div class="d-flex justify-content-around">
              <button
                type="button"
                class="btn"
                jhiTranslate="srsaApp.visaTourism.draftRequests"
                *ngIf="!isGovUser"
                (click)="loadDraftRequests()"
                [ngClass]="getTabClass('draftRequests')"
              >
                قائمة مسودة
              </button>

              <button
                type="button"
                class="btn"
                jhiTranslate="srsaApp.visaTourism.allRequests"
                (click)="loadAllInProgressRequests()"
                [ngClass]="getTabClass('allRequests')"
              >
                allRequests
              </button>
              <button
                type="button"
                class="btn"
                jhiTranslate="srsaApp.visaTourism.needActionRequests"
                *ngIf="isGovUser"
                (click)="loadNeedGovApprovalRequests()"
                [ngClass]="getTabClass('needActionRequests')"
              >
                needActionRequests
              </button>
            </div>
          </div>
          <div class="d-flex gap-4 justify-content-end">
            <div class="col">
              <label class="form-label" jhiTranslate="srsaApp.technicalPermit.permitNumber">رقم الطلب</label>
              <div class="input-group" style="background-color: #ffffff; border-radius: 10px">
                <span class="input-group-text" style="background-color: transparent; cursor: pointer" (click)="onSearch()">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#999" class="bi bi-search" viewBox="0 0 16 16">
                    <path
                      d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"
                    />
                  </svg>
                </span>
                <input
                  type="text"
                  class="form-control"
                  (keyup.enter)="onSearch()"
                  [(ngModel)]="searchRqNumber"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="{{ isArabic() ? 'ابدأ بحثك هنا' : 'Start your search here' }}"
                  aria-label="Search"
                  style="background-color: transparent"
                />
              </div>
            </div>
          </div>
        </div>

        <jhi-alert-error></jhi-alert-error>

        <jhi-alert></jhi-alert>

        <jhi-filter [filters]="filters"></jhi-filter>

        <div class="alert alert-warning" id="no-result" *ngIf="visaTourismRequests?.length === 0">
          <span jhiTranslate="srsaApp.visaTourism.home.notFound">No found</span>
        </div>
<!--        @if (visaTourismRequests && visaTourismRequests.length > 0) {-->
<!--          <jhi-table-->
<!--            [data]="visaTourismRequests"-->
<!--            [columns]="tableColumns"-->
<!--            [tableActions]="tableActions"-->
<!--            [pagination]="true"-->
<!--            [totalItems]="totalItems"-->
<!--            [itemsPerPage]="itemsPerPage"-->
<!--            [page]="page"-->
<!--            (sortChange)="navigateToWithComponentValues($event)"-->
<!--            (pageChange)="navigateToPage($event)"-->
<!--          ></jhi-table>-->
<!--        }-->
        <div class="table-responsive" id="entities" *ngIf="visaTourismRequests && visaTourismRequests.length > 0">
          <table class="table table-striped dga-table" aria-describedby="user-management-page-heading">
            <thead>
            <tr jhiSort  >
              <th scope="col">
                <span jhiTranslate="srsaApp.technicalPermit.permitNumber">permitNumber</span>

                <fa-icon class="p-1"></fa-icon>
              </th>
              <th scope="col">
                <span jhiTranslate="srsaApp.technicalPermit.marineMedium">marineMedium</span>

                <fa-icon class="p-1"></fa-icon>
              </th>
              <th scope="col">
                <span jhiTranslate="srsaApp.technicalPermit.requestStatus">requestStatus</span>

                <fa-icon class="p-1"></fa-icon>
              </th>
              <th scope="col">
                <span jhiTranslate="srsaApp.permitRequest.submitDate">Submit Date</span>
              </th>
              <th scope="col">
                <span jhiTranslate="srsaApp.permitRequest.fees">Fees</span>
              </th>
<!--              <th scope="col">-->
<!--                <div class="d-flex">-->
<!--                  <span jhiTranslate="srsaApp.permitRequest.company">Company</span>-->
<!--                </div>-->
<!--              </th>-->

              <th scope="col">
                <span jhiTranslate="srsaApp.mta.actions"></span>
              </th>
              <th scope="col"></th>
            </tr>
            </thead>
            <tbody>
            <tr data-cy="entityTable" *ngFor="let visaTourismRequest of visaTourismRequests">
              <td>{{ visaTourismRequest!.requestNumber }}</td>
              <td>
                {{ visaTourismRequest?.marineMedium?.nameAr }}
              </td>

              <td [ngStyle]="{ color: ('srsaApp.technicalPermit.status.' + visaTourismRequest?.requestStatus | translate) }">
                  <span
                    *ngIf="visaTourismRequest!?.requestStatus != null"
                    jhiTranslate="srsaApp.technicalPermit.status.{{ visaTourismRequest?.requestStatus }}"
                  >
                  </span>
              </td>
              <td>
                {{ visaTourismRequest!.submitDate | formatMediumDate }}
              </td>

              <td>{{ visaTourismRequest.fees }}</td>
<!--              <td>{{ isArabic() ? visaTourismRequest?.company?.crName : visaTourismRequest?.company?.crName }}</td>-->
              <td class="text-center">
                <div class="dropdown">
                  <button class="btn mk-btn-link rotate-ltr" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <img src="../../../../content/images/Vector.png" alt="Vector" />
                  </button>
                  <ul class="dropdown-menu">
                    <li>
                      <a
                        class="dropdown-item"
                        [routerLink]="['/visa-tourism', visaTourismRequest.id, 'view']"
                        jhiTranslate="entity.action.view"
                      >عرض</a
                      >
                    </li>
                  </ul>
                </div>
              </td>
              <td class="text-end">
                <div class="btn-group"></div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        @if (visaTourismRequests && visaTourismRequests.length > 0) {
          <div>
            <div class="d-flex justify-content-center">
              <p-paginator
                class="dga-paginator"
                [rows]="itemsPerPage"
                [totalRecords]="totalItems"
                [styleClass]="'paginator-medium'"
                (onPageChange)="navigateToPage($event)"
              ></p-paginator>
            </div>
          </div>
        }
      </div>
    </div>
  </div>
</div>
<div
  class="modal fade"
  id="deleteMTA"
  data-bs-backdrop="static"
  data-bs-keyboard="false"
  tabindex="-1"
  aria-labelledby="deleteMTA"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-body text-center mt-4 p-4">
        <p class="sendSubmit mt-4">
          {{ 'srsaApp.mta.deleteAsk' | translate }}
        </p>
        <p class="sendSubmitSub mx-2rem">
          {{ 'srsaApp.mta.noComeBackConfirm' | translate }}
        </p>
        <button type="button" class="btn btn-outline-primary px-5" jhiTranslate="srsaApp.permitRequest.no" data-bs-dismiss="modal"></button>
        <button
          type="button"
          jhiTranslate="srsaApp.permitRequest.yes"
          class="btn btn-primary px-5 mx-2"
          data-bs-toggle="modal"
          (click)="previousState()"
        >
          نعم
        </button>
      </div>
    </div>
  </div>
</div>
