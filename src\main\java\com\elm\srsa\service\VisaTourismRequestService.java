package com.elm.srsa.service;

import com.elm.srsa.config.ApplicationProperties;
import com.elm.srsa.domain.ServiceType;
import com.elm.srsa.domain.visa.VisaApplicant;
import com.elm.srsa.domain.visa.VisaApplicantPreviousTravel;
import com.elm.srsa.domain.visa.VisaRequest;
import com.elm.srsa.domain.visa.VisaTourismRequestAttachment;
import com.elm.srsa.domain.visa.enumerations.*;
import com.elm.srsa.mofa.client.request.*;
import com.elm.srsa.mofa.client.response.ApplicantResponse;
import com.elm.srsa.mofa.client.response.VisaResponse;
import com.elm.srsa.mofa.client.response.VisaTourismApplication;
import com.elm.srsa.mofa.client.service.MofaApiClientService;
import com.elm.srsa.repository.ServiceTypeRepository;
import com.elm.srsa.repository.visa.VisaRequestRepository;
import com.elm.srsa.security.SecurityUtils;
import com.elm.srsa.service.dto.VisaRequestDTO;
import com.elm.srsa.service.dto.VisaRequestSummaryDTO;
import com.elm.srsa.service.mapper.VisaRequestMapper;
import com.elm.srsa.util.LanguageEnum;
import com.elm.srsa.util.PdfGeneratorUtil;
import com.elm.srsa.util.QRCodeGenerator;
import com.elm.srsa.wathq.rest.client.response.newResponse.Activity;
import com.elm.srsa.wathq.rest.client.response.newResponse.CrInfoWathqResponse;
import com.elm.srsa.web.rest.errors.BadRequestAlertException;
import com.elm.srsa.web.rest.request.VisaRequestSearchCriteria;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

import com.google.zxing.WriterException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
public class VisaTourismRequestService {

    private static final String ENTITY_NAME = "VisaTourismRequestService";
    private final ServiceTypeRepository serviceTypeRepository;
    private final MofaApiClientService mofaApiClientService;
    private final VisaRequestRepository visaRequestRepository;
    private final VisaRequestMapper visaRequestMapper;
    private final VisaTourismRequestAttachmentService visaTourismRequestAttachmentService;
    private final ApplicationProperties.HtmlToPdf htmlToPdf;
    private final ApplicationProperties appProperties;
    public VisaTourismRequestService(
        ServiceTypeRepository serviceTypeRepository,
        MofaApiClientService mofaApiClientService,
        VisaRequestRepository visaRequestRepository,
        VisaRequestMapper visaRequestMapper,
        VisaTourismRequestAttachmentService visaTourismRequestAttachmentService, ApplicationProperties appProperties) {
        this.serviceTypeRepository = serviceTypeRepository;
        this.mofaApiClientService = mofaApiClientService;
        this.visaRequestRepository = visaRequestRepository;
        this.visaRequestMapper = visaRequestMapper;
        this.visaTourismRequestAttachmentService = visaTourismRequestAttachmentService;
        this.appProperties = appProperties;
        this.htmlToPdf = this.appProperties.getHtmlToPdf();
    }

    private TourismVisaRequestDTO prepareVisaMofaRequest(List<VisaApplicant> applicants) {
        TourismVisaRequestDTO tourismVisaRequestDTO = new TourismVisaRequestDTO();
        List<ApplicantDTO> mofaApplicants = new ArrayList<>();
        tourismVisaRequestDTO.setApplicants(mofaApplicants);
        for (VisaApplicant visaApplicant : applicants) {
            ApplicantDTO mofaApplicantDTO = new ApplicantDTO();
            mofaApplicantDTO.setAddress(visaApplicant.getAddress());
            mofaApplicantDTO.setBirthCountry(visaApplicant.getBirthCountry());
            mofaApplicantDTO.setEmail(visaApplicant.getEmail());
            mofaApplicantDTO.setBirthPlace(visaApplicant.getBirthCountry());
            mofaApplicantDTO.setApplicationNumber(visaApplicant.getPassportNumber());
            mofaApplicantDTO.setVisaValidity(visaApplicant.getVisaDurationDays());
            mofaApplicantDTO.setDoYouWantDoUmra(visaApplicant.getWantsUmrah());
            mofaApplicantDTO.setBirthDate(visaApplicant.getBirthDate().toString());
            mofaApplicantDTO.setEntryType(visaApplicant.getNumberOfEntries().name().equalsIgnoreCase(EntryType.SINGLE.name()) ? 1 : 2);
            mofaApplicantDTO.setExpectedEntryDate(visaApplicant.getExpectedEntryDate().toString());
            mofaApplicantDTO.setFamilyNameAr(visaApplicant.getFamilyNameAr());
            mofaApplicantDTO.setFamilyNameEn(visaApplicant.getFamilyNameEn());
            mofaApplicantDTO.setFatherNameAr(visaApplicant.getFatherNameAr());
            mofaApplicantDTO.setFatherNameEn(visaApplicant.getFatherNameEn());
            mofaApplicantDTO.setFirstNameAr(visaApplicant.getFirstNameAr());
            mofaApplicantDTO.setFirstNameEn(visaApplicant.getFirstNameEn());
            mofaApplicantDTO.setGender(visaApplicant.getGender().name().equalsIgnoreCase(Gender.MALE.name()) ? 1 : 2);
            mofaApplicantDTO.setGrandFatherNameAr(visaApplicant.getGrandFatherNameAr());
            mofaApplicantDTO.setGrandFatherNameEn(visaApplicant.getGrandFatherNameEn());
            // InsuranceQuestionsDTO
            InsuranceQuestionsDTO insuranceQuestionsDTO = new InsuranceQuestionsDTO();
            insuranceQuestionsDTO.setAreYouDisabledByAccident(visaApplicant.getHasDisability());
            insuranceQuestionsDTO.setCurrentlyInHospitalOrTakingEmerTreatments(visaApplicant.isCurrentlyInHospitalOrTakingEmerTreatments());
            insuranceQuestionsDTO.setYouHaveWeaknessOrCongenitalPlatformation(visaApplicant.isYouHaveWeaknessOrCongenitalPlatformation());
            insuranceQuestionsDTO.setAreYouPregnant(visaApplicant.isAreYouPregnant());
            insuranceQuestionsDTO.setHowManyMonthsOfPregnancy(visaApplicant.getHowManyMonthsOfPregnancy());
            insuranceQuestionsDTO.setNoramalPregnancy(visaApplicant.isNoramalPregnancy());
            mofaApplicantDTO.setInsuranceQuestions(insuranceQuestionsDTO);

            mofaApplicantDTO.setIssuePassportCountry(visaApplicant.getPassportCountry());
            mofaApplicantDTO.setJob(visaApplicant.getJobTitle());
            switch (visaApplicant.getMaritalStatus()) {
                case SINGLE:
                    mofaApplicantDTO.setMaritalstatus(2);
                    break;
                case MARRIED:
                    mofaApplicantDTO.setMaritalstatus(1);
                    break;
                case DIVORCED:
                    mofaApplicantDTO.setMaritalstatus(3);
                    break;
                case WIDOWED:
                    mofaApplicantDTO.setMaritalstatus(4);
                    break;
                default:
                    mofaApplicantDTO.setMaritalstatus(5);
                    break;
            }

            mofaApplicantDTO.setMobileNo(visaApplicant.getPhoneNumber());
            mofaApplicantDTO.setNationality(visaApplicant.getNationality());
            mofaApplicantDTO.setNavigationalLicenseNumber(visaApplicant.getNavigationalLicenseNumber());

            mofaApplicantDTO.setPassportExpiryDate(visaApplicant.getPassportExpiryDate().toString());
            mofaApplicantDTO.setPassportIssueDate(visaApplicant.getPassportIssueDate().toString());
            mofaApplicantDTO.setPassportNo(visaApplicant.getPassportNumber());

            switch (visaApplicant.getPassportType()) {
                case DIPLOMATIC:
                    mofaApplicantDTO.setPassportType(1);
                    break;
                case SPECIAL:
                    mofaApplicantDTO.setMaritalstatus(2);
                    break;
                case NORMAL:
                    mofaApplicantDTO.setMaritalstatus(3);
                    break;
            }

            //          mofaApplicantDTO.setPersonalImage(visaApplicant.getP);

            mofaApplicantDTO.setPrevNationality(visaApplicant.getPrevNationality());
            if (visaApplicant.getPreviousTravels() != null) {
                List<TravelHistoryDTO> travelHistoryDTOS = new ArrayList<>();
                for (VisaApplicantPreviousTravel visaApplicantPreviousTravel : visaApplicant.getPreviousTravels()) {
                    TravelHistoryDTO travelHistoryDTO = new TravelHistoryDTO();
                    travelHistoryDTO.setCountryCode(visaApplicantPreviousTravel.getCountry());
                    travelHistoryDTO.setPurpose(visaApplicantPreviousTravel.getPurpose());
                    travelHistoryDTO.setFromDate(visaApplicantPreviousTravel.getFromDate().toString());
                    travelHistoryDTO.setToDate(visaApplicantPreviousTravel.getToDate().toString());
                    travelHistoryDTOS.add(travelHistoryDTO);
                }
                mofaApplicantDTO.setPrevTravels(travelHistoryDTOS);
            }

            mofaApplicantDTO.setReligion(visaApplicant.getReligion().name().equalsIgnoreCase(Religion.MUSLIM.name()) ? 1 : 2);
            mofaApplicantDTO.setResidenceCountry(visaApplicant.getResidenceCountry());
            mofaApplicantDTO.setGuardianPassportNumber(visaApplicant.getGuardianPassportNnumber());

            // Security Questions
            if (visaApplicant.getSecurityClearance() != null) {
                SecurityQuestionsDTO securityQuestions = new SecurityQuestionsDTO();
                SecurityQuestionDTO securityQuestionDTO = new SecurityQuestionDTO();
                securityQuestionDTO.setAnswer(visaApplicant.getSecurityClearance().getLinkedToTerroristOrg());
                securityQuestions.setDoYouBelongToAnyTerroristOrganization(securityQuestionDTO);

                securityQuestions.setDeliverVaccinationCertificate(visaApplicant.getTookVaccines());
                securityQuestions.setDoesYouPassportContainsRestication(visaApplicant.getSecurityClearance().getHasTravelRestrictions());
                securityQuestions.setDoYouHaveAnyDisibility(visaApplicant.getHasDisability());
                securityQuestions.setHaveYouArrestedByInterpol(visaApplicant.getSecurityClearance().getInterpolArrest());

                securityQuestionDTO = new SecurityQuestionDTO();
                securityQuestionDTO.setAnswer(visaApplicant.getSecurityClearance().getHasBeenArrestedForFinance());
                securityQuestions.setHaveYouArrestedForMoneyCases(securityQuestionDTO);

                securityQuestions.setHaveYouDeportedFromKSAOrOtherCountry(visaApplicant.getSecurityClearance().getDeported());

                securityQuestionDTO = new SecurityQuestionDTO();
                securityQuestionDTO.setAnswer(visaApplicant.getSecurityClearance().getSentencedInHomeCountry());
                securityQuestions.setHaveYouJudjedInYourCountry(securityQuestionDTO);

                securityQuestions.setHaveYouTokeVaccinations(visaApplicant.getTookVaccines());

                securityQuestionDTO = new SecurityQuestionDTO();
                securityQuestionDTO.setAnswer(visaApplicant.getSecurityClearance().getWorkedInPoliticalOrMedia());
                securityQuestions.setHaveYouWorkedInMediaOrPoliticalField(securityQuestionDTO);

                securityQuestionDTO = new SecurityQuestionDTO();
                securityQuestionDTO.setAnswer(visaApplicant.getSecurityClearance().getServedInMilitary());
                securityQuestions.setHaveYouServedInArmyForces(securityQuestionDTO);
                mofaApplicantDTO.setSecurityQuestions(securityQuestions);
            }
        }
        return tourismVisaRequestDTO;
    }

    private VisaTourismApplication applyVisaOnMofa(VisaRequest visaRequest) {
        if (visaRequest != null && visaRequest.getApplicants() != null) {
            TourismVisaRequestDTO tourismVisaRequestDTO = prepareVisaMofaRequest(visaRequest.getApplicants());
            VisaTourismApplication visaTourismApplication = mofaApiClientService.submitTourismVisaRequest(tourismVisaRequestDTO);
            return visaTourismApplication;
        }
        return null;
    }

    private VisaResponse issueVisaOnMofa(VisaRequest visaRequest, VisaTourismApplication visaTourismApplication) {
        if (visaTourismApplication != null && visaTourismApplication.getApplicants() != null) {
            IssueVisaRequest issueVisaRequest = new IssueVisaRequest();
            List<VisaRequestApplicant> visaRequestApplicants = new ArrayList<>();
            for (ApplicantDTO applicantDTO : visaTourismApplication.getApplicants()) {
                VisaRequestApplicant visaRequestApplicant = new VisaRequestApplicant();
                visaRequestApplicant.setApplicantNumber(applicantDTO.getApplicationNumber());
                visaRequestApplicant.setPassportNumber(applicantDTO.getPassportNo());
                visaRequestApplicants.add(visaRequestApplicant);
            }
            issueVisaRequest.setReferenceNumber(visaTourismApplication.getApplicants().get(0).getApplicationNumber());
            issueVisaRequest.setApplicants(visaRequestApplicants);
            VisaResponse issueVisaResponse = mofaApiClientService.issueVisa(issueVisaRequest);
            return issueVisaResponse;
        }
        return null;
    }

    @Transactional
    public VisaResponse applyVisaRequest(VisaRequest entity) {
        VisaTourismApplication visaTourismApplication = applyVisaOnMofa(entity);
        saveApplicantNumber(entity,visaTourismApplication);
        if (visaTourismApplication != null) {
            VisaResponse visaResponse = issueVisaOnMofa(entity, visaTourismApplication);
            saveVisaNumber(entity,visaResponse);
            return visaResponse;
        }
        return null;


    }
    public void saveApplicantNumber(VisaRequest entity,VisaTourismApplication visaTourismApplication) {
        if(entity!=null && visaTourismApplication!=null && visaTourismApplication.getApplicants()!=null)
        {
            for(VisaApplicant visaApplicant : entity.getApplicants())
            {
                for(ApplicantDTO applicantDTO : visaTourismApplication.getApplicants())
                {
                    if(visaApplicant.getPassportNumber().equalsIgnoreCase(applicantDTO.getPassportNo()))
                    {
                        visaApplicant.setApplicantNumber(applicantDTO.getApplicationNumber());
                        break;
                    }
                }
            }
            visaRequestRepository.save(entity);
        }
    }
    public void saveVisaNumber(VisaRequest entity,VisaResponse visaResponse) {
        if(entity!=null && visaResponse!=null && visaResponse.getApplicants()!=null)
        {
            for(VisaApplicant visaApplicant : entity.getApplicants())
            {
                for(ApplicantResponse applicantResponse : visaResponse.getApplicants())
                {
                    if(visaApplicant.getPassportNumber().equalsIgnoreCase(applicantResponse.getPassportNumber()))
                    {
                        visaApplicant.setVisaNumber(applicantResponse.getVisaNumber()+"");
                        visaApplicant.setVisaStatus(applicantResponse.getVisaStatus()+"");
                        break;
                    }
                }
            }
            visaRequestRepository.save(entity);
        }
    }
    @Transactional
    public VisaRequestDTO createRequest(VisaRequestDTO dto, MultipartFile passportCopy, MultipartFile personalImage) {
        calcFees(dto);
        VisaRequest entity = visaRequestMapper.toEntity(dto);
        entity.setRequestStatus(RequestStatus.DRAFT);
        if (entity.getApplicants() != null) {
            entity.getApplicants().forEach(applicant -> applicant.setVisaRequest(entity));
        }

        List<VisaTourismRequestAttachment> attachments = new ArrayList<>();
        saveAttachments(passportCopy, attachments, entity);
        saveAttachments(personalImage, attachments, entity);
        entity.setAttachments(new HashSet<>(attachments));
        VisaRequest saved = visaRequestRepository.save(entity);
        return visaRequestMapper.toDto(saved);
    }

    private void calcFees(VisaRequestDTO dto) {
        Optional<ServiceType> serviceTypeOptional = serviceTypeRepository.findByCode("TVR");
        if (serviceTypeOptional.isPresent() && !dto.getApplicants().isEmpty()) {
            BigDecimal fees = serviceTypeOptional.get().getFees().multiply(BigDecimal.valueOf(dto.getApplicants().size()));
            dto.setTotalFee(fees);
        } else {
            dto.setTotalFee(BigDecimal.ZERO);
        }
    }

    private void saveAttachments(MultipartFile multipartFile, List<VisaTourismRequestAttachment> attachments, VisaRequest visaRequest) {
        if (!Objects.equals(multipartFile.getOriginalFilename(), "no-file-h")) {
            try {
                if (multipartFile.getBytes().length >= 1) {
                    visaTourismRequestAttachmentService.saveFileAndReturnPath(
                        Base64.getEncoder().encodeToString(multipartFile.getBytes()),
                        multipartFile.getOriginalFilename(),
                        visaRequest,
                        attachments
                    );
                }
            } catch (IOException e) {
                throw new RuntimeException("fail to save attachment in visa tourism  request : " + e.getMessage());
            }
        }
    }

    public VisaRequestDTO updateRequest(Long id, VisaRequestDTO dto, MultipartFile passportCopy, MultipartFile personalImage) {
        calcFees(dto);
        return visaRequestRepository
            .findById(id)
            .map(existing -> {
                if (existing.getRequestStatus() != RequestStatus.DRAFT) {
                    throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
                }

                existing.setVisaType(dto.getVisaType());
                existing.setTotalFee(dto.getTotalFee());
                existing.setUpdatedAt(LocalDateTime.now());

                if (dto.getApplicants() != null) {
                    List<VisaApplicant> applicants = dto
                        .getApplicants()
                        .stream()
                        .map(visaRequestMapper::toEntity)
                        .peek(app -> app.setVisaRequest(existing))
                        .toList();

                    existing.getApplicants().clear();
                    existing.getApplicants().addAll(applicants);
                }

                existing.getAttachments().clear();
                List<VisaTourismRequestAttachment> attachments = new ArrayList<>();

                if (passportCopy != null && !passportCopy.isEmpty()) {
                    saveAttachments(passportCopy, attachments, existing);
                }

                if (personalImage != null && !personalImage.isEmpty()) {
                    saveAttachments(personalImage, attachments, existing);
                }
                existing.setAttachments(new HashSet<>(attachments));
                VisaRequest updated = visaRequestRepository.save(existing);
                return visaRequestMapper.toDto(updated);
            })
            .orElseThrow(() -> new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid"));
    }

    @Transactional(readOnly = true)
    public Optional<VisaRequestDTO> getRequestWithApplicants(Long id) {
        return visaRequestRepository.findById(id).map(visaRequestMapper::toDto);
    }

    public List<VisaRequestSummaryDTO> getAllRequestSummaries() {
        List<VisaRequest> requests = visaRequestRepository.findAll();
        return visaRequestMapper.toSummaryDtoList(requests);
    }

    public Page<VisaRequestSummaryDTO> searchVisaRequests(VisaRequestSearchCriteria criteria, Pageable pageable) {
        Specification<VisaRequest> spec = Specification.where(null);

        if (criteria.getRequestNumber() != null && !criteria.getRequestNumber().isBlank()) {
            spec =
                spec.and((root, query, cb) ->
                    cb.like(cb.lower(root.get("requestNumber")), "%" + criteria.getRequestNumber().toLowerCase() + "%")
                );
        }

        if (criteria.getStatuses() != null && !criteria.getStatuses().isEmpty()) {
            spec = spec.and((root, query, cb) -> root.get("requestStatus").in(criteria.getStatuses()));
        }

        Page<VisaRequest> result = visaRequestRepository.findAll(spec, pageable);
        return result.map(visaRequestMapper::toSummaryDto);
    }

    public byte[] exportVisaRequest(Long permitRequestId) throws IOException, WriterException {
        Optional<VisaRequest> optionalVisaRequest = visaRequestRepository.findById(permitRequestId);
        if (optionalVisaRequest.isEmpty()) {
            return null;
        }
        return exportVisaRequest(optionalVisaRequest.get());
    }



    private byte[] exportVisaRequest(VisaRequest permitRequest) throws IOException, WriterException {
        //        1,Maritime Tourism Agent
        //        2,Large Yacht Chartering License
        //        3,Marina Operator License
        //        4,Marine Tour Operator License
        //        5,Cruise Ships Operator License
        String templateFileName = null;
        if (SecurityUtils.isServiceProvider()) {
            templateFileName = "Visa License";
        } else {
            templateFileName = "Visa License";
        }

        if (templateFileName == null) {
            return null;
        }
        String templatePath = "templates/reports";
        String filePath = PdfGeneratorUtil.getFolderPath(templatePath);
        String content = "Constants.EMPTY";
        content = PdfGeneratorUtil.loadTemplate(filePath, content, templateFileName + ".html");
        content = fillContractInfo(permitRequest, content, templateFileName);
        return PdfGeneratorUtil.convertToByte(
            htmlToPdf.getPath(),
            filePath,
            templatePath,
            content,
            false,
            "landscape",
            htmlToPdf.getPdfParams()
        );
    }
// to be filled when add company and user
    private String fillContractInfo(VisaRequest permitRequest, String content, String templateFileName)
        throws IOException, WriterException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, String> map = new HashMap<>();
//        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+3"));
//        String requestNumber = permitRequest.getRequestNumber();
//        String CustomerName = Objects.nonNull(permitRequest.getRequestUserOwner())
//            ? permitRequest.getRequestUserOwner().getFirstName() + " " + permitRequest.getRequestUserOwner().getLastName()
//            : "";
//        String issueDate = Objects.nonNull(permitRequest.getIssueDate()) ? dateFormat.format(permitRequest.getIssueDate()) : null;
//        String expiryDate = Objects.nonNull(permitRequest.getExpiryDate()) ? dateFormat.format(permitRequest.getExpiryDate()) : null;
//        String organizationName = Objects.nonNull(permitRequest.getCompany()) ? permitRequest.getCompany().getCrName() : null;
//        CrInfoWathqResponse companyEnCrInfoWathqResponse = null;
//        if (Objects.nonNull(permitRequest.getCompany())) {
//            companyEnCrInfoWathqResponse =
//                wathqApiResponseService.getCompanyCrInfoWathqResponse(permitRequest.getCompany().getCrNumber(), LanguageEnum.EN);
//        }
//        String organizationNameEn = companyEnCrInfoWathqResponse != null ? companyEnCrInfoWathqResponse.getName() : "";
//        String cityEn = companyEnCrInfoWathqResponse != null ? companyEnCrInfoWathqResponse.getAddress().getNational().getCity() : "";
//        String addressEn = companyEnCrInfoWathqResponse != null
//            ? companyEnCrInfoWathqResponse.getAddress().getNational().getBuildingNumber() +
//            ", " +
//            companyEnCrInfoWathqResponse.getAddress().getNational().getStreetName()
//            : "";
//        String districtEn = companyEnCrInfoWathqResponse != null
//            ? companyEnCrInfoWathqResponse.getAddress().getNational().getDistrict()
//            : "";
//        String crNumber = Objects.nonNull(permitRequest.getCompany()) ? permitRequest.getCompany().getCrNumber() : null;
//        String city = Objects.nonNull(permitRequest.getCompany()) && Objects.nonNull(permitRequest.getCompany().getNationalAddress())
//            ? permitRequest.getCompany().getNationalAddress().getCity()
//            : null;
//        String district = Objects.nonNull(permitRequest.getCompany()) && Objects.nonNull(permitRequest.getCompany().getNationalAddress())
//            ? permitRequest.getCompany().getNationalAddress().getDistrict()
//            : null;
//        String address = Objects.nonNull(permitRequest.getCompany()) && Objects.nonNull(permitRequest.getCompany().getGeneralAddress())
//            ? permitRequest.getCompany().getGeneralAddress().getAddress()
//            : null;
//        CrInfoWathqResponse companyArCrInfoWathqResponse = wathqApiResponseService.getCompanyCrInfoWathqResponse(
//            permitRequest.getCompany().getCrNumber(),
//            LanguageEnum.AR
//        );
//        List<Activity> sortedCompanyActivityList = new ArrayList<>();
//        if (Objects.nonNull(companyArCrInfoWathqResponse) && !CollectionUtils.isEmpty(companyArCrInfoWathqResponse.getActivities())) {
//            List<Activity> companyActivities = companyArCrInfoWathqResponse.getActivities();
//            sortedCompanyActivityList = companyActivities.stream().sorted(Comparator.comparing(Activity::getId)).toList();
//        }
//        map.put("${CustomerName}", CustomerName);
//        map.put("${licenseNumber}", requestNumber);
//        map.put("${issueDate}", issueDate);
//        map.put("${expiryDate}", expiryDate);
//        map.put("${organizationName}", organizationName);
//        map.put("${organizationNameEn}", organizationNameEn);
//        map.put("${crNumber}", crNumber);
//        map.put("${city}", city);
//        map.put("${cityEn}", cityEn);
//        map.put("${district}", district);
//        map.put("${districtEn}", districtEn);
//        map.put("${address}", address);
//        map.put("${addressEn}", addressEn);
//        String baseURL = StringUtils.substringBeforeLast(StringUtils.substringBeforeLast(getCurrentAPIURL(), "/"), "/");
//        String qrCodeLink = baseURL + "/report/" + permitRequest.getUuid();
//        System.out.println("current API ==> " + qrCodeLink);
//        byte[] qrCodeImage = QRCodeGenerator.getQRCodeImage(qrCodeLink, 150, 150);
//        String base64EncodedImage = Base64.getEncoder().encodeToString(qrCodeImage);
//        map.put("${qrcode}", base64EncodedImage);
//        //todo add custom fields for each report
//        switch (templateFileName) {
//            case "VISA REQUEST":
//                break;
//            case "Large Yacht Chartering License":
//                setCompanyActivities(map, sortedCompanyActivityList);
//                break;
//            case "Marina Operator License":
//                String marinaCrNameEn = "";
//                String marinaCrNameAr = "";
//                String marinaOwnerName = "";
//                String marinaCrNumber = "";
//                if (Objects.nonNull(permitRequest.getLicenseRequest())) {
//                    marinaCrNameEn = permitRequest.getLicenseRequest().getMarinaCrNameEn();
//                    marinaCrNameAr = permitRequest.getLicenseRequest().getMarinaCrNameAr();
//                    marinaOwnerName = permitRequest.getLicenseRequest().getMarinaOwnerNameAr();
//                    marinaCrNumber = permitRequest.getLicenseRequest().getMarinaCrNumber();
//                }
//                map.put("${marinaCRNumber}", marinaCrNumber);
//                map.put("${marinaCRNameEN}", marinaCrNameEn);
//                map.put("${marinaCRNameAR}", marinaCrNameAr);
//                // Todo en/ar name!!
//                map.put("${ownerNameAR}", marinaOwnerName);
//                map.put("${ownerNameEN}", marinaOwnerName);
//                break;
//            case "Marine Tour Operator License":
//                setCompanyActivities(map, sortedCompanyActivityList);
//                break;
//            case "Cruise Ships Operator License":
//                setCompanyActivities(map, sortedCompanyActivityList);
//                break;
//            case "Technical License":
//                setCompanyActivities(map, sortedCompanyActivityList);
//                break;
//        }

        return PdfGeneratorUtil.replaceInString(content, map);
    }

    private void setCompanyActivities(Map<String, String> map, List<Activity> sortedCompanyActivityList) {
        if (!sortedCompanyActivityList.isEmpty()) {
            map.put("${mainActivity}", sortedCompanyActivityList.get(0).getName());
        } else {
            map.put("${mainActivity}", "");
            map.put("${subActivity}", "");
        }
        if (sortedCompanyActivityList.size() >= 2) {
            map.put("${subActivity}", sortedCompanyActivityList.get(1).getName());
        } else {
            map.put("${subActivity}", "");
        }
    }
}
