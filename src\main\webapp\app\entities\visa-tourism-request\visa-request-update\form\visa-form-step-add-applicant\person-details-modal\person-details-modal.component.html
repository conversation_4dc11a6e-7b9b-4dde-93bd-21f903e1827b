<div
  class="modal fade"
  id="personDetailsModal"
  tabindex="-1"
  aria-labelledby="personDetailsModalLabel"
  aria-hidden="true"
  data-bs-backdrop="static"
  data-bs-keyboard="false"
>
  <div class="modal-dialog modal-xl modal-dialog-centered">
    <div class="modal-content">
      <!-- Modal Header -->
      <div class="modal-header border-0 pb-0">
        <div class="w-100">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="modal-title fw-bold text-primary" id="personDetailsModalLabel">
              {{ isVerificationStep() ? 'Verification' : 'Add Person Details' }}
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" (click)="closeModal()"></button>
          </div>

          <!-- Progress Bar (only show for form steps) -->
          <div class="progress-container mb-4" *ngIf="isFormStep()">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <span class="text-muted small">Step {{ currentStep }} of {{ totalSteps }}</span>
              <span class="text-muted small">{{ getProgressPercentage() | number: '1.0-0' }}% Complete</span>
            </div>
            <div class="progress" style="height: 8px">
              <div
                class="progress-bar"
                role="progressbar"
                [style.width.%]="getProgressPercentage()"
                [attr.aria-valuenow]="currentStep"
                [attr.aria-valuemin]="1"
                [attr.aria-valuemax]="totalSteps"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="modal-body">
        <!-- Step 0: Verification/Welcome Page (Pop-up 0) -->
        <div *ngIf="currentStep === 0" class="step-content verification-step">
          <form [formGroup]="personForm">
            <div class="row g-3">
              <div class="col-md-6">
                <label for="verificationName" class="form-label required">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.fullName">Full Name</span>
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="verificationName"
                  formControlName="fullNameEn"
                  placeholder="Enter your full name / أدخل اسمك الكامل"
                />
                <div *ngIf="personForm.get('fullNameEn')?.invalid && personForm.get('fullNameEn')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.nameRequired">Full name is required</span>
                </div>
              </div>

              <div class="col-md-6">
                <label for="verificationPassport" class="form-label required">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.passportNumber">Passport Number</span>
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="verificationPassport"
                  formControlName="passportNumber"
                  placeholder="Enter passport number / أدخل رقم جواز السفر"
                />
                <div
                  *ngIf="personForm.get('passportNumber')?.invalid && personForm.get('passportNumber')?.touched"
                  class="text-danger small"
                >
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.passportRequired">Passport number is required</span>
                </div>
              </div>

              <div class="col-md-6">
                <label for="verificationNationality" class="form-label required">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.nationality">Nationality</span>
                </label>
                <select class="form-select" id="verificationNationality" formControlName="nationality">
                  <option value="">Select Nationality / اختر الجنسية</option>
                  <option value="AF">Afghanistan</option>
                  <option value="AL">Albania</option>
                  <option value="DZ">Algeria</option>
                  <option value="AR">Argentina</option>
                  <option value="AU">Australia</option>
                  <option value="AT">Austria</option>
                  <option value="BH">Bahrain</option>
                  <option value="BD">Bangladesh</option>
                  <option value="BE">Belgium</option>
                  <option value="BR">Brazil</option>
                  <option value="CA">Canada</option>
                  <option value="CN">China</option>
                  <option value="EG">Egypt</option>
                  <option value="FR">France</option>
                  <option value="DE">Germany</option>
                  <option value="IN">India</option>
                  <option value="ID">Indonesia</option>
                  <option value="IR">Iran</option>
                  <option value="IQ">Iraq</option>
                  <option value="IT">Italy</option>
                  <option value="JP">Japan</option>
                  <option value="JO">Jordan</option>
                  <option value="KW">Kuwait</option>
                  <option value="LB">Lebanon</option>
                  <option value="MY">Malaysia</option>
                  <option value="MA">Morocco</option>
                  <option value="NL">Netherlands</option>
                  <option value="NG">Nigeria</option>
                  <option value="OM">Oman</option>
                  <option value="PK">Pakistan</option>
                  <option value="PH">Philippines</option>
                  <option value="QA">Qatar</option>
                  <option value="RU">Russia</option>
                  <option value="ES">Spain</option>
                  <option value="LK">Sri Lanka</option>
                  <option value="SD">Sudan</option>
                  <option value="SY">Syria</option>
                  <option value="TH">Thailand</option>
                  <option value="TR">Turkey</option>
                  <option value="AE">United Arab Emirates</option>
                  <option value="GB">United Kingdom</option>
                  <option value="US">United States</option>
                  <option value="YE">Yemen</option>
                </select>
                <div *ngIf="personForm.get('nationality')?.invalid && personForm.get('nationality')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.nationalityRequired">Nationality is required</span>
                </div>
              </div>
            </div>

            <div class="alert alert-info mt-4">
              <h6 class="alert-heading mb-0" jhiTranslate="srsaApp.visaTourism.visaModal.importantNote">Important Note</h6>
              <p class="mb-0" jhiTranslate="srsaApp.visaTourism.visaModal.verificationNote">
                Please ensure the information matches exactly with your passport details. This information will be used throughout the
                application process.
              </p>
            </div>
          </form>
        </div>

        <form [formGroup]="personForm" (ngSubmit)="onSubmit()">
          <!-- Step 1: Personal Information (Pop-up 1) -->
          <div *ngIf="currentStep === 1" class="step-content">
            <h6 class="step-heading mb-4" jhiTranslate="srsaApp.visaTourism.visaModal.personalInformation">Personal Information</h6>
            <div class="row g-3">
              <!-- Arabic Names Section -->
              <div class="col-12">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.arabicNames">Arabic Names</h6>
              </div>
              <div class="col-md-4">
                <label for="firstNameAr" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.firstNameArabic"
                  >First Name (Arabic)</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="firstNameAr"
                  formControlName="firstNameAr"
                  jhiTranslate="srsaApp.visaTourism.visaModal.firstNameArabicPlaceholder"
                  placeholder="First Name"
                  dir="rtl"
                />
                <div *ngIf="personForm.get('firstNameAr')?.invalid && personForm.get('firstNameAr')?.touched" class="text-danger small">
                  <span
                    *ngIf="personForm.get('firstNameAr')?.errors?.['required']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.firstNameArabicRequired"
                    >First name in Arabic is required</span
                  >
                  <span
                    *ngIf="personForm.get('firstNameAr')?.errors?.['arabicOnly']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.arabicOnlyError"
                    >This field should contain only Arabic characters</span
                  >
                </div>
              </div>
              <div class="col-md-4">
                <label for="fatherNameAr" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.fatherNameArabic"
                  >Father Name (Arabic)</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="fatherNameAr"
                  formControlName="fatherNameAr"
                  jhiTranslate="srsaApp.visaTourism.visaModal.fatherNameArabicPlaceholder"
                  placeholder="Father Name"
                  dir="rtl"
                />
                <div *ngIf="personForm.get('fatherNameAr')?.invalid && personForm.get('fatherNameAr')?.touched" class="text-danger small">
                  <span
                    *ngIf="personForm.get('fatherNameAr')?.errors?.['required']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.fatherNameArabicRequired"
                    >Father name in Arabic is required</span
                  >
                  <span
                    *ngIf="personForm.get('fatherNameAr')?.errors?.['arabicOnly']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.arabicOnlyError"
                    >This field should contain only Arabic characters</span
                  >
                </div>
              </div>
              <div class="col-md-4">
                <label
                  for="grandfatherNameAr"
                  class="form-label required"
                  jhiTranslate="srsaApp.visaTourism.visaModal.grandfatherNameArabic"
                  >Grandfather Name (Arabic)</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="grandfatherNameAr"
                  formControlName="grandfatherNameAr"
                  jhiTranslate="srsaApp.visaTourism.visaModal.grandfatherNameArabicPlaceholder"
                  placeholder="Grandfather Name"
                  dir="rtl"
                />
                <div
                  *ngIf="personForm.get('grandfatherNameAr')?.invalid && personForm.get('grandfatherNameAr')?.touched"
                  class="text-danger small"
                >
                  <span
                    *ngIf="personForm.get('grandfatherNameAr')?.errors?.['required']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.grandfatherNameArabicRequired"
                    >Grandfather name in Arabic is required</span
                  >
                  <span
                    *ngIf="personForm.get('grandfatherNameAr')?.errors?.['arabicOnly']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.arabicOnlyError"
                    >This field should contain only Arabic characters</span
                  >
                </div>
              </div>

              <!-- English Names Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.englishNames">English Names</h6>
              </div>
              <div class="col-md-4">
                <label for="firstNameEn" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.firstNameEnglish"
                  >First Name (English)</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="firstNameEn"
                  formControlName="firstNameEn"
                  jhiTranslate="srsaApp.visaTourism.visaModal.firstNameEnglishPlaceholder"
                  placeholder="First Name"
                />
                <div *ngIf="personForm.get('firstNameEn')?.invalid && personForm.get('firstNameEn')?.touched" class="text-danger small">
                  <span
                    *ngIf="personForm.get('firstNameEn')?.errors?.['required']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.firstNameEnglishRequired"
                    >First name in English is required</span
                  >
                  <span
                    *ngIf="personForm.get('firstNameEn')?.errors?.['englishOnly']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.englishOnlyError"
                    >This field should contain only English characters</span
                  >
                </div>
              </div>
              <div class="col-md-4">
                <label for="fatherNameEn" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.fatherNameEnglish"
                  >Father Name (English)</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="fatherNameEn"
                  formControlName="fatherNameEn"
                  jhiTranslate="srsaApp.visaTourism.visaModal.fatherNameEnglishPlaceholder"
                  placeholder="Father Name"
                />
                <div *ngIf="personForm.get('fatherNameEn')?.invalid && personForm.get('fatherNameEn')?.touched" class="text-danger small">
                  <span
                    *ngIf="personForm.get('fatherNameEn')?.errors?.['required']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.fatherNameEnglishRequired"
                    >Father name in English is required</span
                  >
                  <span
                    *ngIf="personForm.get('fatherNameEn')?.errors?.['englishOnly']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.englishOnlyError"
                    >This field should contain only English characters</span
                  >
                </div>
              </div>
              <div class="col-md-4">
                <label
                  for="grandfatherNameEn"
                  class="form-label required"
                  jhiTranslate="srsaApp.visaTourism.visaModal.grandfatherNameEnglish"
                  >Grandfather Name (English)</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="grandfatherNameEn"
                  formControlName="grandfatherNameEn"
                  jhiTranslate="srsaApp.visaTourism.visaModal.grandfatherNameEnglishPlaceholder"
                  placeholder="Grandfather Name"
                />
                <div
                  *ngIf="personForm.get('grandfatherNameEn')?.invalid && personForm.get('grandfatherNameEn')?.touched"
                  class="text-danger small"
                >
                  <span
                    *ngIf="personForm.get('grandfatherNameEn')?.errors?.['required']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.grandfatherNameEnglishRequired"
                    >Grandfather name in English is required</span
                  >
                  <span
                    *ngIf="personForm.get('grandfatherNameEn')?.errors?.['englishOnly']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.englishOnlyError"
                    >This field should contain only English characters</span
                  >
                </div>
              </div>

              <!-- Personal Details Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.personalDetails">Personal Details</h6>
              </div>
              <div class="col-md-6">
                <label for="passportNumber" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.passportNumber"
                  >Passport Number</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="passportNumber"
                  formControlName="passportNumber"
                  jhiTranslate="srsaApp.visaTourism.visaModal.passportNumberPlaceholder"
                  placeholder="Enter passport number"
                />
                <div
                  *ngIf="personForm.get('passportNumber')?.invalid && personForm.get('passportNumber')?.touched"
                  class="text-danger small"
                >
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.passportRequired">Passport number is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="nationality" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.nationality"
                  >Nationality</label
                >
                <select class="form-select" id="nationality" formControlName="nationality">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectNationality">Select Nationality</option>
                  <option value="AF">Afghanistan</option>
                  <option value="AL">Albania</option>
                  <option value="DZ">Algeria</option>
                  <option value="AR">Argentina</option>
                  <option value="AU">Australia</option>
                  <option value="AT">Austria</option>
                  <option value="BH">Bahrain</option>
                  <option value="BD">Bangladesh</option>
                  <option value="BE">Belgium</option>
                  <option value="BR">Brazil</option>
                  <option value="CA">Canada</option>
                  <option value="CN">China</option>
                  <option value="EG">Egypt</option>
                  <option value="FR">France</option>
                  <option value="DE">Germany</option>
                  <option value="IN">India</option>
                  <option value="ID">Indonesia</option>
                  <option value="IR">Iran</option>
                  <option value="IQ">Iraq</option>
                  <option value="IT">Italy</option>
                  <option value="JP">Japan</option>
                  <option value="JO">Jordan</option>
                  <option value="KW">Kuwait</option>
                  <option value="LB">Lebanon</option>
                  <option value="MY">Malaysia</option>
                  <option value="MA">Morocco</option>
                  <option value="NL">Netherlands</option>
                  <option value="NG">Nigeria</option>
                  <option value="OM">Oman</option>
                  <option value="PK">Pakistan</option>
                  <option value="PH">Philippines</option>
                  <option value="QA">Qatar</option>
                  <option value="RU">Russia</option>
                  <option value="ES">Spain</option>
                  <option value="LK">Sri Lanka</option>
                  <option value="SD">Sudan</option>
                  <option value="SY">Syria</option>
                  <option value="TH">Thailand</option>
                  <option value="TR">Turkey</option>
                  <option value="AE">United Arab Emirates</option>
                  <option value="GB">United Kingdom</option>
                  <option value="US">United States</option>
                  <option value="YE">Yemen</option>
                </select>
                <div *ngIf="personForm.get('nationality')?.invalid && personForm.get('nationality')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.nationalityRequired">Nationality is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="documentType" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.documentType"
                  >Document Type</label
                >
                <select class="form-select" id="documentType" formControlName="documentType">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectDocumentType">Select Document Type</option>
                  <option value="PASSPORT" jhiTranslate="srsaApp.visaTourism.visaModal.passport">Passport</option>
                  <option value="NATIONAL_ID" jhiTranslate="srsaApp.visaTourism.visaModal.nationalId">National ID</option>
                  <option value="RESIDENCE_PERMIT" jhiTranslate="srsaApp.visaTourism.visaModal.residencePermit">Residence Permit</option>
                </select>
                <div *ngIf="personForm.get('documentType')?.invalid && personForm.get('documentType')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.documentTypeRequired">Document type is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="passportType" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.passportType"
                  >Passport Type</label
                >
                <select class="form-select" id="passportType" formControlName="passportType">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectPassportType">Select Type</option>
                  <option value="ORDINARY" jhiTranslate="srsaApp.visaTourism.visaModal.ordinary">Ordinary</option>
                  <option value="DIPLOMATIC" jhiTranslate="srsaApp.visaTourism.visaModal.diplomatic">Diplomatic</option>
                  <option value="SERVICE" jhiTranslate="srsaApp.visaTourism.visaModal.service">Service</option>
                  <option value="OFFICIAL" jhiTranslate="srsaApp.visaTourism.visaModal.official">Official</option>
                </select>
                <div *ngIf="personForm.get('passportType')?.invalid && personForm.get('passportType')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.passportTypeRequired">Passport type is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="birthDate" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.birthDate">Birth Date</label>
                <gregorian-calendar
                  name="birthDate"
                  id="birthDate"
                  formControlName="birthDate"
                  [minDateAsToday]="false"
                  [maxDateAsToday]="true"
                  placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                >
                </gregorian-calendar>
                <div *ngIf="personForm.get('birthDate')?.invalid && personForm.get('birthDate')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.birthDateRequired">Birth date is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="gender" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.gender">Gender</label>
                <select class="form-select" id="gender" formControlName="gender">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectGender">Select Gender</option>
                  <option value="MALE" jhiTranslate="srsaApp.visaTourism.visaModal.male">Male</option>
                  <option value="FEMALE" jhiTranslate="srsaApp.visaTourism.visaModal.female">Female</option>
                </select>
                <div *ngIf="personForm.get('gender')?.invalid && personForm.get('gender')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.genderRequired">Gender is required</span>
                </div>
              </div>

              <!-- Passport Photo Upload Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.passportPhoto">Passport Photo</h6>
              </div>
              <div class="col-12">
                <label for="passportPhoto" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.uploadPassportPhoto"
                  >Upload Passport Photo</label
                >
                <div>
                  <dropzone
                    id="passportPhoto"
                    #dropzonePassportPhoto
                    [config]="getPassportPhotoConfig()"
                    (addedFile)="onPassportPhotoSuccess($event)"
                    (error)="onPassportPhotoError($event)"
                    (removedFile)="onPassportPhotoRemoved($event)"
                  >
                  </dropzone>
                  <div *ngIf="passportPhotoError" class="text-danger small mt-2">
                    <span jhiTranslate="srsaApp.visaTourism.visaModal.fileUploadError">File upload error</span>
                  </div>
                  <div *ngIf="hasPassportPhoto && !passportPhotoError" class="text-success small mt-2">
                    <i class="fas fa-check-circle me-1"></i>
                    <span>{{ passportPhotoName }} uploaded successfully</span>
                  </div>
                </div>
                <div *ngIf="personForm.get('passportPhoto')?.invalid && personForm.get('passportPhoto')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.passportPhotoRequired">Passport photo is required</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 2: Additional Information (Pop-up 2) -->
          <div *ngIf="currentStep === 2" class="step-content">
            <h6 class="step-heading mb-4" jhiTranslate="srsaApp.visaTourism.visaModal.additionalInformation">Additional Information</h6>
            <div class="row g-3">
              <!-- Location Information Section -->
              <div class="col-12">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.locationInformation">Location Information</h6>
              </div>
              <div class="col-md-6">
                <label for="birthCountry" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.birthCountry"
                  >Birth Country</label
                >
                <select class="form-select" id="birthCountry" formControlName="birthCountry">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectBirthCountry">Select Birth Country</option>
                  <option value="AF">Afghanistan</option>
                  <option value="AL">Albania</option>
                  <option value="DZ">Algeria</option>
                  <option value="AR">Argentina</option>
                  <option value="AU">Australia</option>
                  <option value="AT">Austria</option>
                  <option value="BH">Bahrain</option>
                  <option value="BD">Bangladesh</option>
                  <option value="BE">Belgium</option>
                  <option value="BR">Brazil</option>
                  <option value="CA">Canada</option>
                  <option value="CN">China</option>
                  <option value="EG">Egypt</option>
                  <option value="FR">France</option>
                  <option value="DE">Germany</option>
                  <option value="IN">India</option>
                  <option value="ID">Indonesia</option>
                  <option value="IR">Iran</option>
                  <option value="IQ">Iraq</option>
                  <option value="IT">Italy</option>
                  <option value="JP">Japan</option>
                  <option value="JO">Jordan</option>
                  <option value="KW">Kuwait</option>
                  <option value="LB">Lebanon</option>
                  <option value="MY">Malaysia</option>
                  <option value="MA">Morocco</option>
                  <option value="NL">Netherlands</option>
                  <option value="NG">Nigeria</option>
                  <option value="OM">Oman</option>
                  <option value="PK">Pakistan</option>
                  <option value="PH">Philippines</option>
                  <option value="QA">Qatar</option>
                  <option value="RU">Russia</option>
                  <option value="SA">Saudi Arabia</option>
                  <option value="ES">Spain</option>
                  <option value="LK">Sri Lanka</option>
                  <option value="SD">Sudan</option>
                  <option value="SY">Syria</option>
                  <option value="TH">Thailand</option>
                  <option value="TR">Turkey</option>
                  <option value="AE">United Arab Emirates</option>
                  <option value="GB">United Kingdom</option>
                  <option value="US">United States</option>
                  <option value="YE">Yemen</option>
                </select>
                <div *ngIf="personForm.get('birthCountry')?.invalid && personForm.get('birthCountry')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.birthCountryRequired">Birth country is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="residenceCountry" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.residenceCountry"
                  >Residence Country</label
                >
                <select class="form-select" id="residenceCountry" formControlName="residenceCountry">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectResidenceCountry">Select Residence Country</option>
                  <option value="AF">Afghanistan</option>
                  <option value="AL">Albania</option>
                  <option value="DZ">Algeria</option>
                  <option value="AR">Argentina</option>
                  <option value="AU">Australia</option>
                  <option value="AT">Austria</option>
                  <option value="BH">Bahrain</option>
                  <option value="BD">Bangladesh</option>
                  <option value="BE">Belgium</option>
                  <option value="BR">Brazil</option>
                  <option value="CA">Canada</option>
                  <option value="CN">China</option>
                  <option value="EG">Egypt</option>
                  <option value="FR">France</option>
                  <option value="DE">Germany</option>
                  <option value="IN">India</option>
                  <option value="ID">Indonesia</option>
                  <option value="IR">Iran</option>
                  <option value="IQ">Iraq</option>
                  <option value="IT">Italy</option>
                  <option value="JP">Japan</option>
                  <option value="JO">Jordan</option>
                  <option value="KW">Kuwait</option>
                  <option value="LB">Lebanon</option>
                  <option value="MY">Malaysia</option>
                  <option value="MA">Morocco</option>
                  <option value="NL">Netherlands</option>
                  <option value="NG">Nigeria</option>
                  <option value="OM">Oman</option>
                  <option value="PK">Pakistan</option>
                  <option value="PH">Philippines</option>
                  <option value="QA">Qatar</option>
                  <option value="RU">Russia</option>
                  <option value="SA">Saudi Arabia</option>
                  <option value="ES">Spain</option>
                  <option value="LK">Sri Lanka</option>
                  <option value="SD">Sudan</option>
                  <option value="SY">Syria</option>
                  <option value="TH">Thailand</option>
                  <option value="TR">Turkey</option>
                  <option value="AE">United Arab Emirates</option>
                  <option value="GB">United Kingdom</option>
                  <option value="US">United States</option>
                  <option value="YE">Yemen</option>
                </select>
                <div
                  *ngIf="personForm.get('residenceCountry')?.invalid && personForm.get('residenceCountry')?.touched"
                  class="text-danger small"
                >
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.residenceCountryRequired">Residence country is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="birthPlace" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.birthPlace"
                  >Birth Place</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="birthPlace"
                  formControlName="birthPlace"
                  jhiTranslate="srsaApp.visaTourism.visaModal.birthPlacePlaceholder"
                  placeholder="Enter birth place"
                />
                <div *ngIf="personForm.get('birthPlace')?.invalid && personForm.get('birthPlace')?.touched" class="text-danger small">
                  <span
                    *ngIf="personForm.get('birthPlace')?.errors?.['required']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.birthPlaceRequired"
                    >Birth place is required</span
                  >
                  <span
                    *ngIf="personForm.get('birthPlace')?.errors?.['arabicOrEnglish']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.arabicOrEnglishError"
                    >This field should contain only Arabic or English characters</span
                  >
                </div>
              </div>
              <div class="col-md-6">
                <label for="countryCode" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.countryCode"
                  >Country Code</label
                >
                <input
                  type="text"
                  class="form-control"
                  id="countryCode"
                  formControlName="countryCode"
                  jhiTranslate="srsaApp.visaTourism.visaModal.countryCodePlaceholder"
                  placeholder="Enter country code"
                />
                <div *ngIf="personForm.get('countryCode')?.invalid && personForm.get('countryCode')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.countryCodeRequired">Country code is required</span>
                </div>
              </div>

              <!-- Passport Details Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.passportDetails">Passport Details</h6>
              </div>
              <div class="col-md-6">
                <label for="passportIssueDate" class="form-label required">
                  <span>Passport Issue Date</span>
                  <span class="text-muted ms-2">تاريخ إصدار الجواز</span>
                </label>
                <gregorian-calendar
                  name="passportIssueDate"
                  id="passportIssueDate"
                  formControlName="passportIssueDate"
                  [minDateAsToday]="false"
                  [maxDateAsToday]="false"
                  placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                >
                </gregorian-calendar>
                <div
                  *ngIf="personForm.get('passportIssueDate')?.invalid && personForm.get('passportIssueDate')?.touched"
                  class="text-danger small"
                >
                  Passport issue date is required / تاريخ إصدار الجواز مطلوب
                </div>
              </div>
              <div class="col-md-6">
                <label for="passportExpiryDate" class="form-label required">
                  <span>Passport Expiry Date</span>
                  <span class="text-muted ms-2">تاريخ انتهاء الجواز</span>
                </label>
                <gregorian-calendar
                  name="passportExpiryDate"
                  id="passportExpiryDate"
                  formControlName="passportExpiryDate"
                  [minDateAsToday]="true"
                  [maxDateAsToday]="false"
                  placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                >
                </gregorian-calendar>
                <div
                  *ngIf="personForm.get('passportExpiryDate')?.invalid && personForm.get('passportExpiryDate')?.touched"
                  class="text-danger small"
                >
                  Passport expiry date is required / تاريخ انتهاء الجواز مطلوب
                </div>
              </div>
              <div class="col-md-6">
                <label for="passportCountry" class="form-label required">
                  <span>Passport Issuing Country</span>
                  <span class="text-muted ms-2">دولة إصدار الجواز</span>
                </label>
                <select class="form-select" id="passportCountry" formControlName="passportCountry">
                  <option value="">Select Issuing Country / اختر دولة الإصدار</option>
                  <option value="AF">Afghanistan</option>
                  <option value="AL">Albania</option>
                  <option value="DZ">Algeria</option>
                  <option value="AR">Argentina</option>
                  <option value="AU">Australia</option>
                  <option value="AT">Austria</option>
                  <option value="BH">Bahrain</option>
                  <option value="BD">Bangladesh</option>
                  <option value="BE">Belgium</option>
                  <option value="BR">Brazil</option>
                  <option value="CA">Canada</option>
                  <option value="CN">China</option>
                  <option value="EG">Egypt</option>
                  <option value="FR">France</option>
                  <option value="DE">Germany</option>
                  <option value="IN">India</option>
                  <option value="ID">Indonesia</option>
                  <option value="IR">Iran</option>
                  <option value="IQ">Iraq</option>
                  <option value="IT">Italy</option>
                  <option value="JP">Japan</option>
                  <option value="JO">Jordan</option>
                  <option value="KW">Kuwait</option>
                  <option value="LB">Lebanon</option>
                  <option value="MY">Malaysia</option>
                  <option value="MA">Morocco</option>
                  <option value="NL">Netherlands</option>
                  <option value="NG">Nigeria</option>
                  <option value="OM">Oman</option>
                  <option value="PK">Pakistan</option>
                  <option value="PH">Philippines</option>
                  <option value="QA">Qatar</option>
                  <option value="RU">Russia</option>
                  <option value="SA">Saudi Arabia</option>
                  <option value="ES">Spain</option>
                  <option value="LK">Sri Lanka</option>
                  <option value="SD">Sudan</option>
                  <option value="SY">Syria</option>
                  <option value="TH">Thailand</option>
                  <option value="TR">Turkey</option>
                  <option value="AE">United Arab Emirates</option>
                  <option value="GB">United Kingdom</option>
                  <option value="US">United States</option>
                  <option value="YE">Yemen</option>
                </select>
                <div
                  *ngIf="personForm.get('passportCountry')?.invalid && personForm.get('passportCountry')?.touched"
                  class="text-danger small"
                >
                  Passport issuing country is required / دولة إصدار الجواز مطلوبة
                </div>
              </div>

              <!-- Visa Information Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.visaInformation">Visa Information</h6>
              </div>
              <div class="col-md-6">
                <label for="visaType" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.visaType">Visa Type</label>
                <select class="form-select" id="visaType" formControlName="visaType">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectVisaType">Select Visa Type</option>
                  <option value="TOURISM" jhiTranslate="srsaApp.visaTourism.visaModal.tourism">Tourism</option>
                  <option value="BUSINESS" jhiTranslate="srsaApp.visaTourism.visaModal.business">Business</option>
                  <option value="TRANSIT" jhiTranslate="srsaApp.visaTourism.visaModal.transit">Transit</option>
                  <option value="FAMILY_VISIT" jhiTranslate="srsaApp.visaTourism.visaModal.familyVisit">Family Visit</option>
                  <option value="UMRAH" jhiTranslate="srsaApp.visaTourism.visaModal.umrah">Umrah</option>
                </select>
                <div *ngIf="personForm.get('visaType')?.invalid && personForm.get('visaType')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.visaTypeRequired">Visa type is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="numberOfEntries" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.numberOfEntries"
                  >Number of Entries</label
                >
                <select class="form-select" id="numberOfEntries" formControlName="numberOfEntries">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectNumberOfEntries">Select Number of Entries</option>
                  <option value="SINGLE" jhiTranslate="srsaApp.visaTourism.visaModal.singleEntry">Single Entry</option>
                  <option value="MULTIPLE" jhiTranslate="srsaApp.visaTourism.visaModal.multipleEntry">Multiple Entry</option>
                </select>
                <div
                  *ngIf="personForm.get('numberOfEntries')?.invalid && personForm.get('numberOfEntries')?.touched"
                  class="text-danger small"
                >
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.numberOfEntriesRequired">Number of entries is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="visaDuration" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.visaDuration"
                  >Visa Duration (Days)</label
                >
                <input
                  type="number"
                  class="form-control"
                  id="visaDuration"
                  formControlName="visaDuration"
                  jhiTranslate="srsaApp.visaTourism.visaModal.visaDurationPlaceholder"
                  placeholder="Enter visa duration in days"
                  min="1"
                  max="365"
                />
                <div *ngIf="personForm.get('visaDuration')?.invalid && personForm.get('visaDuration')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.visaDurationRequired">Visa duration is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="visaValidity" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.visaValidity"
                  >Visa Validity</label
                >
                <gregorian-calendar
                  name="visaValidity"
                  id="visaValidity"
                  formControlName="visaValidity"
                  [minDateAsToday]="true"
                  [maxDateAsToday]="false"
                  placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                >
                </gregorian-calendar>
                <div *ngIf="personForm.get('visaValidity')?.invalid && personForm.get('visaValidity')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.visaValidityRequired">Visa validity is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="expectedEntryDate" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.expectedEntryDate"
                  >Expected Entry Date</label
                >
                <gregorian-calendar
                  name="expectedEntryDate"
                  id="expectedEntryDate"
                  formControlName="expectedEntryDate"
                  [minDateAsToday]="true"
                  [maxDateAsToday]="false"
                  placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                >
                </gregorian-calendar>
                <div
                  *ngIf="personForm.get('expectedEntryDate')?.invalid && personForm.get('expectedEntryDate')?.touched"
                  class="text-danger small"
                >
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.expectedEntryDateRequired">Expected entry date is required</span>
                </div>
              </div>

              <!-- Contact Information Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.contactInformation">Contact Information</h6>
              </div>
              <div class="col-md-6">
                <label for="phoneNumber" class="form-label required">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.mobileNumber">Mobile Number</span>
                </label>
                <input
                  type="tel"
                  class="form-control"
                  id="phoneNumber"
                  formControlName="phoneNumber"
                  placeholder="Enter mobile number / أدخل رقم الجوال"
                />
                <div *ngIf="personForm.get('phoneNumber')?.invalid && personForm.get('phoneNumber')?.touched" class="text-danger small">
                  Mobile number is required / رقم الجوال مطلوب
                </div>
              </div>
              <div class="col-md-6">
                <label for="email" class="form-label required">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.emailAddress">Email Address</span>
                </label>
                <input
                  type="email"
                  class="form-control"
                  id="email"
                  formControlName="email"
                  placeholder="Enter email address / أدخل البريد الإلكتروني"
                />
                <div *ngIf="personForm.get('email')?.invalid && personForm.get('email')?.touched" class="text-danger small">
                  <span *ngIf="personForm.get('email')?.errors?.['required']">Email is required / البريد الإلكتروني مطلوب</span>
                  <span *ngIf="personForm.get('email')?.errors?.['email']">Please enter a valid email / يرجى إدخال بريد إلكتروني صحيح</span>
                </div>
              </div>

              <!-- Additional Nationality Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.additionalNationality">
                  Additional Nationality
                </h6>
              </div>
              <div class="col-12">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.hasOtherNationalityQuestion"
                  >Do you have another nationality?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasOtherNationality"
                        id="hasOtherNationalityYes"
                        value="true"
                        formControlName="hasOtherNationality"
                      />
                      <label class="form-check-label" for="hasOtherNationalityYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasOtherNationality"
                        id="hasOtherNationalityNo"
                        value="false"
                        formControlName="hasOtherNationality"
                      />
                      <label class="form-check-label" for="hasOtherNationalityNo" jhiTranslate="srsaApp.visaTourism.visaModal.no">No</label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Guardian Information Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.guardianInformation">Guardian Information</h6>
              </div>
              <div class="col-md-6">
                <label for="guardianName" class="form-label">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.guardianName">Guardian Name</span>
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="guardianName"
                  formControlName="guardianName"
                  placeholder="Enter guardian name / أدخل اسم الوصي"
                />
              </div>
              <div class="col-md-6">
                <label for="guardianRelation" class="form-label">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.guardianRelation">Relationship with Guardian</span>
                </label>
                <select class="form-select" id="guardianRelation" formControlName="guardianRelation">
                  <option value="">Select Relationship / اختر صلة القرابة</option>
                  <option value="FATHER">Father / الأب</option>
                  <option value="MOTHER">Mother / الأم</option>
                  <option value="BROTHER">Brother / الأخ</option>
                  <option value="SISTER">Sister / الأخت</option>
                  <option value="UNCLE">Uncle / العم</option>
                  <option value="AUNT">Aunt / العمة</option>
                  <option value="GRANDFATHER">Grandfather / الجد</option>
                  <option value="GRANDMOTHER">Grandmother / الجدة</option>
                  <option value="OTHER">Other / أخرى</option>
                </select>
              </div>
              <div class="col-md-6">
                <label for="guardianPassportNumber" class="form-label">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.guardianPassportNumber">Guardian Passport Number</span>
                </label>
                <input
                  type="text"
                  class="form-control"
                  id="guardianPassportNumber"
                  formControlName="guardianPassportNumber"
                  placeholder="Enter guardian passport number / أدخل رقم جواز سفر الوصي"
                />
              </div>
              <div class="col-md-6">
                <label for="guardianAge" class="form-label">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.guardianAge">Guardian Age</span>
                </label>
                <input
                  type="number"
                  class="form-control"
                  id="guardianAge"
                  formControlName="guardianAge"
                  placeholder="Enter guardian age / أدخل عمر الوصي"
                  min="18"
                  max="100"
                />
              </div>
            </div>
          </div>

          <!-- Step 3: Additional Details -->
          <div *ngIf="currentStep === 3" class="step-content">
            <h6 class="step-heading mb-4" jhiTranslate="srsaApp.visaTourism.visaModal.additionalDetails">Additional Details</h6>
            <div class="row g-3">
              <!-- Personal Status Section -->
              <div class="col-12">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.personalStatus">Personal Status</h6>
              </div>
              <div class="col-md-6">
                <label for="maritalStatus" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.maritalStatus"
                  >Marital Status</label
                >
                <select class="form-select" id="maritalStatus" formControlName="maritalStatus">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectMaritalStatus">Select Marital Status</option>
                  <option value="SINGLE" jhiTranslate="srsaApp.visaTourism.visaModal.single">Single</option>
                  <option value="MARRIED" jhiTranslate="srsaApp.visaTourism.visaModal.married">Married</option>
                  <option value="DIVORCED" jhiTranslate="srsaApp.visaTourism.visaModal.divorced">Divorced</option>
                  <option value="WIDOWED" jhiTranslate="srsaApp.visaTourism.visaModal.widowed">Widowed</option>
                </select>
                <div *ngIf="personForm.get('maritalStatus')?.invalid && personForm.get('maritalStatus')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.maritalStatusRequired">Marital status is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="religion" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.religion">Religion</label>
                <select class="form-select" id="religion" formControlName="religion">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectReligion">Select Religion</option>
                  <option value="ISLAM" jhiTranslate="srsaApp.visaTourism.visaModal.islam">Islam</option>
                  <option value="CHRISTIANITY" jhiTranslate="srsaApp.visaTourism.visaModal.christianity">Christianity</option>
                  <option value="JUDAISM" jhiTranslate="srsaApp.visaTourism.visaModal.judaism">Judaism</option>
                  <option value="HINDUISM" jhiTranslate="srsaApp.visaTourism.visaModal.hinduism">Hinduism</option>
                  <option value="BUDDHISM" jhiTranslate="srsaApp.visaTourism.visaModal.buddhism">Buddhism</option>
                  <option value="OTHER" jhiTranslate="srsaApp.visaTourism.visaModal.other">Other</option>
                </select>
                <div *ngIf="personForm.get('religion')?.invalid && personForm.get('religion')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.religionRequired">Religion is required</span>
                </div>
              </div>

              <!-- Address Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.addressInformation">Address Information</h6>
              </div>
              <div class="col-12">
                <label for="address" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.address">Address</label>
                <textarea
                  class="form-control"
                  id="address"
                  formControlName="address"
                  rows="3"
                  jhiTranslate="srsaApp.visaTourism.visaModal.addressPlaceholder"
                  placeholder="Enter your full address"
                ></textarea>
                <div *ngIf="personForm.get('address')?.invalid && personForm.get('address')?.touched" class="text-danger small">
                  <span *ngIf="personForm.get('address')?.errors?.['required']" jhiTranslate="srsaApp.visaTourism.visaModal.addressRequired"
                    >Address is required</span
                  >
                  <span
                    *ngIf="personForm.get('address')?.errors?.['arabicOrEnglish']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.arabicOrEnglishError"
                    >This field should contain only Arabic or English characters</span
                  >
                </div>
              </div>

              <!-- Work Information Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.workInformation">Work Information</h6>
              </div>
              <div class="col-md-6">
                <label for="occupation" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.occupation"
                  >Nature of Work</label
                >
                <select class="form-select" id="occupation" formControlName="occupation">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectOccupation">Select Occupation</option>
                  <option value="ENGINEER" jhiTranslate="srsaApp.visaTourism.visaModal.engineer">Engineer</option>
                  <option value="DOCTOR" jhiTranslate="srsaApp.visaTourism.visaModal.doctor">Doctor</option>
                  <option value="TEACHER" jhiTranslate="srsaApp.visaTourism.visaModal.teacher">Teacher</option>
                  <option value="BUSINESSMAN" jhiTranslate="srsaApp.visaTourism.visaModal.businessman">Businessman</option>
                  <option value="STUDENT" jhiTranslate="srsaApp.visaTourism.visaModal.student">Student</option>
                  <option value="RETIRED" jhiTranslate="srsaApp.visaTourism.visaModal.retired">Retired</option>
                  <option value="UNEMPLOYED" jhiTranslate="srsaApp.visaTourism.visaModal.unemployed">Unemployed</option>
                  <option value="OTHER" jhiTranslate="srsaApp.visaTourism.visaModal.other">Other</option>
                </select>
                <div *ngIf="personForm.get('occupation')?.invalid && personForm.get('occupation')?.touched" class="text-danger small">
                  <span
                    *ngIf="personForm.get('occupation')?.errors?.['required']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.occupationRequired"
                    >Nature of work is required</span
                  >
                  <span
                    *ngIf="personForm.get('occupation')?.errors?.['arabicOrEnglish']"
                    jhiTranslate="srsaApp.visaTourism.visaModal.arabicOrEnglishError"
                    >This field should contain only Arabic or English characters</span
                  >
                </div>
              </div>

              <!-- Visa Information Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.visaInformation">Visa Information</h6>
              </div>
              <div class="col-md-6">
                <label for="visaType" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.visaType">Visa Type</label>
                <select class="form-select" id="visaType" formControlName="visaType">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectVisaType">Select Visa Type</option>
                  <option value="TOURISM" jhiTranslate="srsaApp.visaTourism.visaModal.tourism">Tourism</option>
                  <option value="BUSINESS" jhiTranslate="srsaApp.visaTourism.visaModal.business">Business</option>
                  <option value="FAMILY_VISIT" jhiTranslate="srsaApp.visaTourism.visaModal.familyVisit">Family Visit</option>
                  <option value="MEDICAL" jhiTranslate="srsaApp.visaTourism.visaModal.medical">Medical</option>
                  <option value="UMRAH" jhiTranslate="srsaApp.visaTourism.visaModal.umrah">Umrah</option>
                </select>
                <div *ngIf="personForm.get('visaType')?.invalid && personForm.get('visaType')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.visaTypeRequired">Visa type is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="numberOfEntries" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.numberOfEntries"
                  >Number of Entries</label
                >
                <select class="form-select" id="numberOfEntries" formControlName="numberOfEntries">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectNumberOfEntries">Select Number of Entries</option>
                  <option value="SINGLE" jhiTranslate="srsaApp.visaTourism.visaModal.singleEntry">Single Entry</option>
                  <option value="MULTIPLE" jhiTranslate="srsaApp.visaTourism.visaModal.multipleEntry">Multiple Entry</option>
                </select>
                <div
                  *ngIf="personForm.get('numberOfEntries')?.invalid && personForm.get('numberOfEntries')?.touched"
                  class="text-danger small"
                >
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.numberOfEntriesRequired">Number of entries is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="visaDuration" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.visaDuration"
                  >Visa Duration (Days)</label
                >
                <select class="form-select" id="visaDuration" formControlName="visaDuration">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectVisaDuration">Select Visa Duration</option>
                  <option value="30">30 Days</option>
                  <option value="60">60 Days</option>
                  <option value="90">90 Days</option>
                  <option value="180">180 Days</option>
                  <option value="365">1 Year</option>
                </select>
                <div *ngIf="personForm.get('visaDuration')?.invalid && personForm.get('visaDuration')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.visaDurationRequired">Visa duration is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="visaValidity" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.visaValidity"
                  >Visa Validity</label
                >
                <select class="form-select" id="visaValidity" formControlName="visaValidity">
                  <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectVisaValidity">Select Visa Validity</option>
                  <option value="3_MONTHS">3 Months</option>
                  <option value="6_MONTHS">6 Months</option>
                  <option value="1_YEAR">1 Year</option>
                  <option value="2_YEARS">2 Years</option>
                  <option value="5_YEARS">5 Years</option>
                </select>
                <div *ngIf="personForm.get('visaValidity')?.invalid && personForm.get('visaValidity')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.visaValidityRequired">Visa validity is required</span>
                </div>
              </div>
              <div class="col-md-6">
                <label for="expectedEntryDate" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.expectedEntryDate"
                  >Expected Entry Date</label
                >
                <gregorian-calendar
                  name="expectedEntryDate"
                  id="expectedEntryDate"
                  formControlName="expectedEntryDate"
                  [minDateAsToday]="true"
                  [maxDateAsToday]="false"
                  placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                >
                </gregorian-calendar>
                <div
                  *ngIf="personForm.get('expectedEntryDate')?.invalid && personForm.get('expectedEntryDate')?.touched"
                  class="text-danger small"
                >
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.expectedEntryDateRequired">Expected entry date is required</span>
                </div>
              </div>

              <!-- Personal Photo Upload Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.personalPhoto">Personal Photo</h6>
              </div>
              <div class="col-12">
                <label for="personalPhoto" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.uploadPersonalPhoto"
                  >Upload Personal Photo</label
                >
                <div>
                  <dropzone
                    id="personalPhoto"
                    #dropzonePersonalPhoto
                    [config]="getPersonalPhotoConfig()"
                    (addedFile)="onPersonalPhotoSuccess($event)"
                    (error)="onPersonalPhotoError($event)"
                    (removedFile)="onPersonalPhotoRemoved($event)"
                  >
                  </dropzone>
                  <div *ngIf="personalPhotoError" class="text-danger small mt-2">
                    <span jhiTranslate="srsaApp.visaTourism.visaModal.fileUploadError">File upload error</span>
                  </div>
                  <div *ngIf="hasPersonalPhoto && !personalPhotoError" class="text-success small mt-2">
                    <i class="fas fa-check-circle me-1"></i>
                    <span>{{ personalPhotoName }} uploaded successfully</span>
                  </div>
                </div>
                <div *ngIf="personForm.get('personalPhoto')?.invalid && personForm.get('personalPhoto')?.touched" class="text-danger small">
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.personalPhotoRequired">Personal photo is required</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 4: Travel History -->
          <div *ngIf="currentStep === 4" class="step-content">
            <h6 class="step-heading mb-4" jhiTranslate="srsaApp.visaTourism.visaModal.travelHistory">Travel History</h6>
            <div class="row g-3">
              <!-- Previous Travel Question -->
              <div class="col-12">
                <label class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.hasPreviousTravelQuestion"
                  >Do you have previous travel history?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasPreviousTravel"
                        id="hasPreviousTravelYes"
                        value="true"
                        formControlName="hasPreviousTravel"
                        (change)="onPreviousTravelChange(true)"
                      />
                      <label class="form-check-label" for="hasPreviousTravelYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasPreviousTravel"
                        id="hasPreviousTravelNo"
                        value="false"
                        formControlName="hasPreviousTravel"
                        (change)="onPreviousTravelChange(false)"
                      />
                      <label class="form-check-label" for="hasPreviousTravelNo" jhiTranslate="srsaApp.visaTourism.visaModal.no">No</label>
                    </div>
                  </div>
                </div>
                <div
                  *ngIf="personForm.get('hasPreviousTravel')?.invalid && personForm.get('hasPreviousTravel')?.touched"
                  class="text-danger small"
                >
                  <span jhiTranslate="srsaApp.visaTourism.visaModal.previousTravelRequired"
                    >Please select if you have previous travel history</span
                  >
                </div>
              </div>

              <!-- Travel Details Section (shown only if Yes is selected) -->
              <div *ngIf="showTravelDetails" class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.travelDetails">Travel Details</h6>

                <div class="row g-3">
                  <div class="col-md-6">
                    <label
                      for="previousCountries"
                      class="form-label required"
                      jhiTranslate="srsaApp.visaTourism.visaModal.previousCountries"
                      >Previous Travel Countries</label
                    >
                    <select class="form-select" id="previousCountries" formControlName="previousCountries" multiple>
                      <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectPreviousCountries">
                        Select Previous Countries
                      </option>
                      <option value="AF">Afghanistan</option>
                      <option value="AL">Albania</option>
                      <option value="DZ">Algeria</option>
                      <option value="AR">Argentina</option>
                      <option value="AU">Australia</option>
                      <option value="AT">Austria</option>
                      <option value="BH">Bahrain</option>
                      <option value="BD">Bangladesh</option>
                      <option value="BE">Belgium</option>
                      <option value="BR">Brazil</option>
                      <option value="CA">Canada</option>
                      <option value="CN">China</option>
                      <option value="EG">Egypt</option>
                      <option value="FR">France</option>
                      <option value="DE">Germany</option>
                      <option value="IN">India</option>
                      <option value="ID">Indonesia</option>
                      <option value="IR">Iran</option>
                      <option value="IQ">Iraq</option>
                      <option value="IT">Italy</option>
                      <option value="JP">Japan</option>
                      <option value="JO">Jordan</option>
                      <option value="KW">Kuwait</option>
                      <option value="LB">Lebanon</option>
                      <option value="MY">Malaysia</option>
                      <option value="MA">Morocco</option>
                      <option value="NL">Netherlands</option>
                      <option value="NG">Nigeria</option>
                      <option value="OM">Oman</option>
                      <option value="PK">Pakistan</option>
                      <option value="PH">Philippines</option>
                      <option value="QA">Qatar</option>
                      <option value="RU">Russia</option>
                      <option value="SA">Saudi Arabia</option>
                      <option value="ES">Spain</option>
                      <option value="LK">Sri Lanka</option>
                      <option value="SD">Sudan</option>
                      <option value="SY">Syria</option>
                      <option value="TH">Thailand</option>
                      <option value="TR">Turkey</option>
                      <option value="AE">United Arab Emirates</option>
                      <option value="GB">United Kingdom</option>
                      <option value="US">United States</option>
                      <option value="YE">Yemen</option>
                    </select>
                    <div
                      *ngIf="personForm.get('previousCountries')?.invalid && personForm.get('previousCountries')?.touched"
                      class="text-danger small"
                    >
                      <span jhiTranslate="srsaApp.visaTourism.visaModal.previousCountriesRequired"
                        >Previous travel countries are required</span
                      >
                    </div>
                  </div>

                  <div class="col-md-6">
                    <label for="travelPurpose" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.travelPurpose"
                      >Purpose of Travel</label
                    >
                    <select class="form-select" id="travelPurpose" formControlName="travelPurpose">
                      <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectTravelPurpose">Select Purpose</option>
                      <option value="TOURISM" jhiTranslate="srsaApp.visaTourism.visaModal.tourism">Tourism</option>
                      <option value="BUSINESS" jhiTranslate="srsaApp.visaTourism.visaModal.business">Business</option>
                      <option value="EDUCATION" jhiTranslate="srsaApp.visaTourism.visaModal.education">Education</option>
                      <option value="WORK" jhiTranslate="srsaApp.visaTourism.visaModal.work">Work</option>
                      <option value="FAMILY_VISIT" jhiTranslate="srsaApp.visaTourism.visaModal.familyVisit">Family Visit</option>
                      <option value="MEDICAL" jhiTranslate="srsaApp.visaTourism.visaModal.medical">Medical</option>
                      <option value="RELIGIOUS" jhiTranslate="srsaApp.visaTourism.visaModal.religious">Religious</option>
                      <option value="OTHER" jhiTranslate="srsaApp.visaTourism.visaModal.other">Other</option>
                    </select>
                    <div
                      *ngIf="personForm.get('travelPurpose')?.invalid && personForm.get('travelPurpose')?.touched"
                      class="text-danger small"
                    >
                      <span jhiTranslate="srsaApp.visaTourism.visaModal.travelPurposeRequired">Purpose of travel is required</span>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <label for="travelFromDate" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.travelFromDate"
                      >Travel From Date</label
                    >
                    <gregorian-calendar
                      name="travelFromDate"
                      id="travelFromDate"
                      formControlName="travelFromDate"
                      [minDateAsToday]="false"
                      [maxDateAsToday]="false"
                      placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                    >
                    </gregorian-calendar>
                    <div
                      *ngIf="personForm.get('travelFromDate')?.invalid && personForm.get('travelFromDate')?.touched"
                      class="text-danger small"
                    >
                      <span jhiTranslate="srsaApp.visaTourism.visaModal.travelFromDateRequired">Travel from date is required</span>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <label for="travelToDate" class="form-label required" jhiTranslate="srsaApp.visaTourism.visaModal.travelToDate"
                      >Travel To Date</label
                    >
                    <gregorian-calendar
                      name="travelToDate"
                      id="travelToDate"
                      formControlName="travelToDate"
                      [minDateAsToday]="false"
                      [maxDateAsToday]="false"
                      placeholder="{{ 'global.form.placeholderMandatory' | translate }}"
                    >
                    </gregorian-calendar>
                    <div
                      *ngIf="personForm.get('travelToDate')?.invalid && personForm.get('travelToDate')?.touched"
                      class="text-danger small"
                    >
                      <span jhiTranslate="srsaApp.visaTourism.visaModal.travelToDateRequired">Travel to date is required</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 5: Health and Insurance Questions -->
          <div *ngIf="currentStep === 5" class="step-content">
            <h6 class="step-heading mb-4" jhiTranslate="srsaApp.visaTourism.visaModal.healthInsuranceQuestions">
              Health and Insurance Questions
            </h6>
            <div class="row g-3">
              <!-- Disability Question -->
              <div class="col-12">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.disabilityInformation">
                  Disability Information
                </h6>
              </div>
              <div class="col-12">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.hasDisabilityFromAccident"
                  >Have you been in an accident that caused you a disability?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasDisabilityFromAccident"
                        id="hasDisabilityFromAccidentYes"
                        value="true"
                        formControlName="hasDisabilityFromAccident"
                      />
                      <label class="form-check-label" for="hasDisabilityFromAccidentYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasDisabilityFromAccident"
                        id="hasDisabilityFromAccidentNo"
                        value="false"
                        formControlName="hasDisabilityFromAccident"
                      />
                      <label class="form-check-label" for="hasDisabilityFromAccidentNo" jhiTranslate="srsaApp.visaTourism.visaModal.no"
                        >No</label
                      >
                    </div>
                  </div>
                </div>
              </div>

              <!-- Hospital/Emergency Treatment Question -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.medicalTreatment">Medical Treatment</h6>
              </div>
              <div class="col-12">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.currentlyHospitalized"
                  >Are you currently hospitalized or receiving emergency treatment?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="currentlyHospitalized"
                        id="currentlyHospitalizedYes"
                        value="true"
                        formControlName="currentlyHospitalized"
                      />
                      <label class="form-check-label" for="currentlyHospitalizedYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="currentlyHospitalized"
                        id="currentlyHospitalizedNo"
                        value="false"
                        formControlName="currentlyHospitalized"
                      />
                      <label class="form-check-label" for="currentlyHospitalizedNo" jhiTranslate="srsaApp.visaTourism.visaModal.no"
                        >No</label
                      >
                    </div>
                  </div>
                </div>
              </div>

              <!-- Congenital Condition Question -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.congenitalConditions">
                  Congenital Conditions
                </h6>
              </div>
              <div class="col-12">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.hasCongenitalCondition"
                  >Do you suffer from any weakness or congenital deformity?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasCongenitalCondition"
                        id="hasCongenitalConditionYes"
                        value="true"
                        formControlName="hasCongenitalCondition"
                      />
                      <label class="form-check-label" for="hasCongenitalConditionYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasCongenitalCondition"
                        id="hasCongenitalConditionNo"
                        value="false"
                        formControlName="hasCongenitalCondition"
                      />
                      <label class="form-check-label" for="hasCongenitalConditionNo" jhiTranslate="srsaApp.visaTourism.visaModal.no"
                        >No</label
                      >
                    </div>
                  </div>
                </div>
              </div>

              <!-- Pregnancy Questions -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.pregnancyInformation">
                  Pregnancy Information
                </h6>
              </div>
              <div class="col-12">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.currentlyPregnant">Are you currently pregnant?</label>
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="currentlyPregnant"
                        id="currentlyPregnantYes"
                        value="true"
                        formControlName="currentlyPregnant"
                        (change)="onPregnancyChange($event)"
                      />
                      <label class="form-check-label" for="currentlyPregnantYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="currentlyPregnant"
                        id="currentlyPregnantNo"
                        value="false"
                        formControlName="currentlyPregnant"
                        (change)="onPregnancyChange($event)"
                      />
                      <label class="form-check-label" for="currentlyPregnantNo" jhiTranslate="srsaApp.visaTourism.visaModal.no">No</label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Pregnancy Details (shown only if pregnant) -->
              <div *ngIf="showPregnancyDetails" class="col-12 mt-3">
                <div class="row g-3">
                  <div class="col-md-6">
                    <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.pregnancyFromAssistance"
                      >Is the current pregnancy a result of assisted reproductive methods?</label
                    >
                    <div class="row mt-2">
                      <div class="col-6">
                        <div class="form-check">
                          <input
                            class="form-check-input"
                            type="radio"
                            name="pregnancyFromAssistance"
                            id="pregnancyFromAssistanceYes"
                            value="true"
                            formControlName="pregnancyFromAssistance"
                          />
                          <label class="form-check-label" for="pregnancyFromAssistanceYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                            >Yes</label
                          >
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="form-check">
                          <input
                            class="form-check-input"
                            type="radio"
                            name="pregnancyFromAssistance"
                            id="pregnancyFromAssistanceNo"
                            value="false"
                            formControlName="pregnancyFromAssistance"
                          />
                          <label class="form-check-label" for="pregnancyFromAssistanceNo" jhiTranslate="srsaApp.visaTourism.visaModal.no"
                            >No</label
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <label for="pregnancyMonth" class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.pregnancyMonth"
                      >Which month of pregnancy?</label
                    >
                    <select class="form-select" id="pregnancyMonth" formControlName="pregnancyMonth">
                      <option value="" jhiTranslate="srsaApp.visaTourism.visaModal.selectPregnancyMonth">Select Month</option>
                      <option value="1" jhiTranslate="srsaApp.visaTourism.visaModal.month1">1st Month</option>
                      <option value="2" jhiTranslate="srsaApp.visaTourism.visaModal.month2">2nd Month</option>
                      <option value="3" jhiTranslate="srsaApp.visaTourism.visaModal.month3">3rd Month</option>
                      <option value="4" jhiTranslate="srsaApp.visaTourism.visaModal.month4">4th Month</option>
                      <option value="5" jhiTranslate="srsaApp.visaTourism.visaModal.month5">5th Month</option>
                      <option value="6" jhiTranslate="srsaApp.visaTourism.visaModal.month6">6th Month</option>
                      <option value="7" jhiTranslate="srsaApp.visaTourism.visaModal.month7">7th Month</option>
                      <option value="8" jhiTranslate="srsaApp.visaTourism.visaModal.month8">8th Month</option>
                      <option value="9" jhiTranslate="srsaApp.visaTourism.visaModal.month9">9th Month</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 6: Health Information -->
          <div *ngIf="currentStep === 6" class="step-content">
            <h6 class="step-heading mb-4" jhiTranslate="srsaApp.visaTourism.visaModal.healthInformation">Health Information</h6>
            <div class="row g-3">
              <!-- Disability Question -->
              <div class="col-12">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.hasDisability"
                  >Does the person have any disability?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasDisability"
                        id="hasDisabilityYes"
                        value="true"
                        formControlName="hasDisability"
                      />
                      <label class="form-check-label" for="hasDisabilityYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes">Yes</label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasDisability"
                        id="hasDisabilityNo"
                        value="false"
                        formControlName="hasDisability"
                      />
                      <label class="form-check-label" for="hasDisabilityNo" jhiTranslate="srsaApp.visaTourism.visaModal.no">No</label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Vaccination Question -->
              <div class="col-12 mt-4">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.tookVaccines"
                  >Does the person take vaccinations?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="tookVaccines"
                        id="tookVaccinesYes"
                        value="true"
                        formControlName="tookVaccines"
                      />
                      <label class="form-check-label" for="tookVaccinesYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes">Yes</label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="tookVaccines"
                        id="tookVaccinesNo"
                        value="false"
                        formControlName="tookVaccines"
                      />
                      <label class="form-check-label" for="tookVaccinesNo" jhiTranslate="srsaApp.visaTourism.visaModal.no">No</label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Vaccination Commitment Question -->
              <div class="col-12 mt-4">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.commitToProvideVaccinations"
                  >Do you commit to providing original vaccinations when needed?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="commitToProvideVaccinations"
                        id="commitToProvideVaccinationsYes"
                        value="true"
                        formControlName="commitToProvideVaccinations"
                      />
                      <label class="form-check-label" for="commitToProvideVaccinationsYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="commitToProvideVaccinations"
                        id="commitToProvideVaccinationsNo"
                        value="false"
                        formControlName="commitToProvideVaccinations"
                      />
                      <label class="form-check-label" for="commitToProvideVaccinationsNo" jhiTranslate="srsaApp.visaTourism.visaModal.no"
                        >No</label
                      >
                    </div>
                  </div>
                </div>
              </div>

              <!-- Legal Questions Section -->
              <div class="col-12 mt-4">
                <h6 class="section-subtitle mb-3" jhiTranslate="srsaApp.visaTourism.visaModal.legalQuestions">Legal Questions</h6>
              </div>

              <!-- Interpol Question -->
              <div class="col-12">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.hasInterpolWarrant"
                  >Has an arrest warrant been issued against the person by the International Police (Interpol)?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasInterpolWarrant"
                        id="hasInterpolWarrantYes"
                        value="true"
                        formControlName="hasInterpolWarrant"
                      />
                      <label class="form-check-label" for="hasInterpolWarrantYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasInterpolWarrant"
                        id="hasInterpolWarrantNo"
                        value="false"
                        formControlName="hasInterpolWarrant"
                      />
                      <label class="form-check-label" for="hasInterpolWarrantNo" jhiTranslate="srsaApp.visaTourism.visaModal.no">No</label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Deportation Question -->
              <div class="col-12 mt-4">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.hasBeenDeported"
                  >Has the person been previously deported or expelled from any country, including violation of residence
                  regulations?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasBeenDeported"
                        id="hasBeenDeportedYes"
                        value="true"
                        formControlName="hasBeenDeported"
                      />
                      <label class="form-check-label" for="hasBeenDeportedYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes">Yes</label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasBeenDeported"
                        id="hasBeenDeportedNo"
                        value="false"
                        formControlName="hasBeenDeported"
                      />
                      <label class="form-check-label" for="hasBeenDeportedNo" jhiTranslate="srsaApp.visaTourism.visaModal.no">No</label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Passport Restrictions Question -->
              <div class="col-12 mt-4">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.hasPassportRestrictions"
                  >Does the person's passport contain any restrictions, conditions, or notes valid for one trip only?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasPassportRestrictions"
                        id="hasPassportRestrictionsYes"
                        value="true"
                        formControlName="hasPassportRestrictions"
                      />
                      <label class="form-check-label" for="hasPassportRestrictionsYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasPassportRestrictions"
                        id="hasPassportRestrictionsNo"
                        value="false"
                        formControlName="hasPassportRestrictions"
                      />
                      <label class="form-check-label" for="hasPassportRestrictionsNo" jhiTranslate="srsaApp.visaTourism.visaModal.no"
                        >No</label
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 7: Legal and Financial Questions -->
          <div *ngIf="currentStep === 7" class="step-content">
            <h6 class="step-heading mb-4" jhiTranslate="srsaApp.visaTourism.visaModal.legalFinancialQuestions">
              Legal and Financial Questions
            </h6>
            <div class="row g-3">
              <!-- Financial Legal Question -->
              <div class="col-12">
                <label class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.hasFinancialArrest"
                  >Has the person been arrested due to financial issues?</label
                >
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasFinancialArrest"
                        id="hasFinancialArrestYes"
                        value="true"
                        formControlName="hasFinancialArrest"
                        (change)="onFinancialArrestChange($event)"
                      />
                      <label class="form-check-label" for="hasFinancialArrestYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasFinancialArrest"
                        id="hasFinancialArrestNo"
                        value="false"
                        formControlName="hasFinancialArrest"
                        (change)="onFinancialArrestChange($event)"
                      />
                      <label class="form-check-label" for="hasFinancialArrestNo" jhiTranslate="srsaApp.visaTourism.visaModal.no">No</label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Clarifications Text Input (shown when Yes is selected) -->
              <div *ngIf="showFinancialArrestDetails" class="col-12 mt-3">
                <label for="financialArrestClarifications" class="form-label" jhiTranslate="srsaApp.visaTourism.visaModal.clarifications"
                  >Clarifications</label
                >
                <textarea
                  class="form-control"
                  id="financialArrestClarifications"
                  formControlName="financialArrestClarifications"
                  rows="4"
                  jhiTranslate="srsaApp.visaTourism.visaModal.clarificationsPlaceholder"
                  placeholder="Please provide clarifications"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Step 8: Legal Questions -->
          <div *ngIf="currentStep === 8" class="step-content">
            <div class="row g-3">
              <!-- Question: هل تم القبض على الشخص في بلده؟ -->
              <div class="col-12">
                <label class="form-label">هل تم القبض على الشخص في بلده؟</label>
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasBeenArrested"
                        id="hasBeenArrestedYes"
                        value="true"
                        formControlName="hasBeenArrested"
                      />
                      <label class="form-check-label" for="hasBeenArrestedYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes">Yes</label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasBeenArrested"
                        id="hasBeenArrestedNo"
                        value="false"
                        formControlName="hasBeenArrested"
                      />
                      <label class="form-check-label" for="hasBeenArrestedNo" jhiTranslate="srsaApp.visaTourism.visaModal.no">No</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 9: Background Check -->
          <div *ngIf="currentStep === 9" class="step-content">
            <div class="row g-3">
              <!-- Question: هل عمل الشخص فى المجال الاعلامي او المجال السياسي؟ -->
              <div class="col-12">
                <label class="form-label">هل عمل الشخص فى المجال الاعلامي او المجال السياسي؟</label>
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasMediaPoliticalWork"
                        id="hasMediaPoliticalWorkYes"
                        value="true"
                        formControlName="hasMediaPoliticalWork"
                      />
                      <label class="form-check-label" for="hasMediaPoliticalWorkYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasMediaPoliticalWork"
                        id="hasMediaPoliticalWorkNo"
                        value="false"
                        formControlName="hasMediaPoliticalWork"
                      />
                      <label class="form-check-label" for="hasMediaPoliticalWorkNo" jhiTranslate="srsaApp.visaTourism.visaModal.no"
                        >No</label
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 10: Security Questions -->
          <div *ngIf="currentStep === 10" class="step-content">
            <div class="row g-3">
              <!-- Question: هل يننتمي الشخص لاى منظمة ارهابية ؟ -->
              <div class="col-12">
                <label class="form-label">هل يننتمي الشخص لاى منظمة ارهابية ؟</label>
                <div class="row mt-2">
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasTerroristAffiliation"
                        id="hasTerroristAffiliationYes"
                        value="true"
                        formControlName="hasTerroristAffiliation"
                      />
                      <label class="form-check-label" for="hasTerroristAffiliationYes" jhiTranslate="srsaApp.visaTourism.visaModal.yes"
                        >Yes</label
                      >
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="radio"
                        name="hasTerroristAffiliation"
                        id="hasTerroristAffiliationNo"
                        value="false"
                        formControlName="hasTerroristAffiliation"
                      />
                      <label class="form-check-label" for="hasTerroristAffiliationNo" jhiTranslate="srsaApp.visaTourism.visaModal.no"
                        >No</label
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer border-0 pt-0">
        <div class="d-flex justify-content-between w-100">
          <button
            type="button"
            class="btn btn-outline-secondary"
            [disabled]="currentStep === 0"
            (click)="previousStep()"
            *ngIf="!isVerificationStep()"
          >
            <span jhiTranslate="srsaApp.visaTourism.visaModal.previous">Previous</span>
          </button>

          <div class="d-flex gap-2">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" (click)="closeModal()">
              <span jhiTranslate="srsaApp.visaTourism.visaModal.close">Close</span>
            </button>

            <!-- Start Application button for verification step -->
            <button
              *ngIf="isVerificationStep()"
              type="button"
              class="btn btn-primary"
              [disabled]="!isCurrentStepValid()"
              (click)="nextStep()"
            >
              <span jhiTranslate="srsaApp.visaTourism.visaModal.startApplication">
                {{ isArabic() ? 'ابدأ التطبيق' : 'Start Application' }}
              </span>
            </button>

            <!-- Next button for form steps -->
            <button
              *ngIf="isFormStep() && currentStep < totalSteps"
              type="button"
              class="btn btn-primary"
              [disabled]="!isCurrentStepValid()"
              (click)="nextStep()"
            >
              <span jhiTranslate="srsaApp.visaTourism.visaModal.next">Next</span>
            </button>

            <!-- Submit button for final step -->
            <button
              *ngIf="currentStep === totalSteps"
              type="button"
              class="btn btn-success"
              [disabled]="!isFormValidForSubmission() || isSubmitting"
              (click)="onSubmit()"
            >
              <span *ngIf="!isSubmitting"> <i class="fas fa-check me-2"></i>Submit </span>
              <span *ngIf="isSubmitting"> <i class="fas fa-spinner fa-spin me-2"></i>Submitting... </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
