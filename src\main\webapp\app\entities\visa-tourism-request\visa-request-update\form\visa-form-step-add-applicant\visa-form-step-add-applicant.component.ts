import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import TranslateDirective from 'app/shared/language/translate.directive';
import { VisaApplicant } from 'app/entities/visa-tourism-request/visa-applicant.model';
import { PersonListTableComponent } from 'app/entities/visa-tourism-request/visa-request-update/form/visa-form-step-add-applicant/person-list-table/person-list-table.component';
import { PersonDetailsModalComponent } from './person-details-modal/person-details-modal.component';

@Component({
  selector: 'jhi-visa-form-step-add-applicant',
  standalone: true,
  imports: [CommonModule, TranslateModule, TranslateDirective, PersonDetailsModalComponent, PersonListTableComponent],
  templateUrl: './visa-form-step-add-applicant.component.html',
  styleUrl: './visa-form-step-add-applicant.component.scss',
})
export class VisaFormStepAddApplicantComponent {
  applicants: VisaApplicant[] = [];

  onPersonAdded(person: VisaApplicant): void {
    // Check for duplicate passport numbers
    const existingPerson = this.applicants.find(applicant => applicant.passportNumber === person.passportNumber);

    if (existingPerson) {
      console.warn('Person with this passport number already exists:', person.passportNumber);
      // You might want to show a user-friendly message here
      alert('A person with this passport number already exists in the list.');
      return;
    }

    // Add unique identifier if not present
    if (!person.id) {
      person.id = this.generateUniqueId();
    }

    this.applicants.push(person);
    console.log('Person added to list:', person);
    console.log('Total applicants:', this.applicants.length);
  }

  onPersonRemoved(personId: string): void {
    const index = this.applicants.findIndex(applicant => applicant.id === personId);
    if (index > -1) {
      const removedPerson = this.applicants.splice(index, 1)[0];
      console.log('Person removed from list:', removedPerson);
      console.log('Total applicants:', this.applicants.length);
    }
  }

  onPersonEdited(updatedPerson: VisaApplicant): void {
    const index = this.applicants.findIndex(applicant => applicant.id === updatedPerson.id);
    if (index > -1) {
      // Check for duplicate passport numbers (excluding the current person)
      const duplicatePerson = this.applicants.find(
        (applicant, idx) => idx !== index && applicant.passportNumber === updatedPerson.passportNumber,
      );

      if (duplicatePerson) {
        console.warn('Another person with this passport number already exists:', updatedPerson.passportNumber);
        alert('Another person with this passport number already exists in the list.');
        return;
      }

      this.applicants[index] = updatedPerson;
      console.log('Person updated in list:', updatedPerson);
    }
  }

  // Generate a unique ID for new persons
  private generateUniqueId(): string {
    return 'person_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Get the list of applicants ready for API submission
  getApplicantsForSubmission(): VisaApplicant[] {
    return this.applicants.map(applicant => ({
      ...applicant,
      // Ensure all required fields are present for API
      id: applicant.id || this.generateUniqueId(),
      fullName: applicant.fullName || '',
      passportNumber: applicant.passportNumber || '',
      nationality: applicant.nationality || '',
      birthDate: applicant.birthDate || '',
      gender: applicant.gender || '',
      identityType: applicant.identityType || 'PASSPORT',
      role: applicant.role || 'PASSENGER',
    }));
  }

  // Check if the applicant list is valid for submission
  isApplicantListValid(): boolean {
    if (this.applicants.length === 0) {
      return false;
    }

    return this.applicants.every(
      applicant => applicant.fullName && applicant.passportNumber && applicant.nationality && applicant.birthDate && applicant.gender,
    );
  }

  // Get validation errors for the applicant list
  getApplicantListValidationErrors(): string[] {
    const errors: string[] = [];

    if (this.applicants.length === 0) {
      errors.push('At least one applicant is required');
    }

    this.applicants.forEach((applicant, index) => {
      if (!applicant.fullName) {
        errors.push(`Applicant ${index + 1}: Full name is required`);
      }
      if (!applicant.passportNumber) {
        errors.push(`Applicant ${index + 1}: Passport number is required`);
      }
      if (!applicant.nationality) {
        errors.push(`Applicant ${index + 1}: Nationality is required`);
      }
      if (!applicant.birthDate) {
        errors.push(`Applicant ${index + 1}: Birth date is required`);
      }
      if (!applicant.gender) {
        errors.push(`Applicant ${index + 1}: Gender is required`);
      }
    });

    return errors;
  }
}
