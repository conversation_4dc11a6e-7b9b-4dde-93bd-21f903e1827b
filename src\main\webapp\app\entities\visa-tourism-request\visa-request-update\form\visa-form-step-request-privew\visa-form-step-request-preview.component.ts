import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { VisaApplicant } from 'app/entities/visa-tourism-request/visa-applicant.model';
import { MarineMedium } from 'app/entities/marine-medium/model/marine-medium.model';
import { VisaTourismRequestDataService } from '../../shared/visa-tourism-request-data.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'jhi-visa-form-step-request-preview',
  standalone: true,
  imports: [CommonModule, TranslateModule],
  templateUrl: './visa-form-step-request-preview.component.html',
  styleUrl: './visa-form-step-request-preview.component.scss',
})
export class VisaFormStepRequestPreviewComponent implements OnInit {
  private dataService = inject(VisaTourismRequestDataService);
  private translateService = inject(TranslateService);

  selectedMarineMedium: MarineMedium | null = null;
  applicants: VisaApplicant[] = [];

  ngOnInit(): void {
    this.loadPreviewData();
  }

  private loadPreviewData(): void {
    this.selectedMarineMedium = this.dataService.getSelectedMarineMedium();
    this.applicants = this.dataService.getApplicants();
  }

  isArabic(): boolean {
    const currentLang = this.translateService.currentLang || 'ar';
    return currentLang.startsWith('ar');
  }

  getMarineMediumName(): string {
    if (!this.selectedMarineMedium) return 'N/A';
    return this.isArabic()
      ? this.selectedMarineMedium.nameAr || this.selectedMarineMedium.nameEn || 'N/A'
      : this.selectedMarineMedium.nameEn || this.selectedMarineMedium.nameAr || 'N/A';
  }

  getMarineMediumType(): string {
    if (!this.selectedMarineMedium?.marineMediumType) return 'N/A';
    return this.isArabic()
      ? this.selectedMarineMedium.marineMediumType.nameAr || this.selectedMarineMedium.marineMediumType.nameEn || 'N/A'
      : this.selectedMarineMedium.marineMediumType.nameEn || this.selectedMarineMedium.marineMediumType.nameAr || 'N/A';
  }

  getFlagNationality(): string {
    if (!this.selectedMarineMedium?.flagNationality) return 'N/A';
    return this.isArabic()
      ? this.selectedMarineMedium.flagNationality.nameAr || this.selectedMarineMedium.flagNationality.nameEn || 'N/A'
      : this.selectedMarineMedium.flagNationality.nameEn || this.selectedMarineMedium.flagNationality.nameAr || 'N/A';
  }

  onEditMarineMedium(): void {
    // Navigate back to marine medium selection step
    this.dataService.navigateToStep(1);
  }

  onEditApplicants(): void {
    // Navigate back to applicants step
    this.dataService.navigateToStep(2);
  }

  onEditApplicant(applicant: VisaApplicant): void {
    // Navigate back to applicants step with specific applicant selected for editing
    this.dataService.setSelectedApplicantForEdit(applicant);
    this.dataService.navigateToStep(2);
  }

  onRemoveApplicant(applicant: VisaApplicant): void {
    if (applicant.id) {
      this.dataService.removeApplicant(applicant.id);
      this.applicants = this.dataService.getApplicants();
    }
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(this.isArabic() ? 'ar-SA' : 'en-US');
    } catch {
      return dateString;
    }
  }

  formatBoolean(value: boolean): string {
    return value ? this.translateService.instant('global.yes') : this.translateService.instant('global.no');
  }
}
