import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { VisaApplicant } from '../../visa-applicant.model';
import { MarineMedium } from '../../../marine-medium/model/marine-medium.model';

@Injectable({
  providedIn: 'root',
})
export class VisaTourismRequestDataService {
  private selectedMarineMediumSubject = new BehaviorSubject<MarineMedium | null>(null);
  private applicantsSubject = new BehaviorSubject<VisaApplicant[]>([]);
  private currentStepSubject = new BehaviorSubject<number>(1);
  private selectedApplicantForEditSubject = new BehaviorSubject<VisaApplicant | null>(null);

  // Observables
  selectedMarineMedium$ = this.selectedMarineMediumSubject.asObservable();
  applicants$ = this.applicantsSubject.asObservable();
  currentStep$ = this.currentStepSubject.asObservable();
  selectedApplicantForEdit$ = this.selectedApplicantForEditSubject.asObservable();

  // Marine Medium methods
  setSelectedMarineMedium(marineMedium: MarineMedium | null): void {
    this.selectedMarineMediumSubject.next(marineMedium);
  }

  getSelectedMarineMedium(): MarineMedium | null {
    return this.selectedMarineMediumSubject.value;
  }

  // Applicants methods
  setApplicants(applicants: VisaApplicant[]): void {
    this.applicantsSubject.next(applicants);
  }

  getApplicants(): VisaApplicant[] {
    return this.applicantsSubject.value;
  }

  addApplicant(applicant: VisaApplicant): void {
    const currentApplicants = this.getApplicants();
    // Generate a unique ID if not provided
    if (!applicant.id) {
      applicant.id = this.generateUniqueId();
    }
    this.setApplicants([...currentApplicants, applicant]);
  }

  updateApplicant(updatedApplicant: VisaApplicant): void {
    const currentApplicants = this.getApplicants();
    const index = currentApplicants.findIndex(app => app.id === updatedApplicant.id);
    if (index !== -1) {
      currentApplicants[index] = updatedApplicant;
      this.setApplicants([...currentApplicants]);
    }
  }

  removeApplicant(applicantId: string): void {
    const currentApplicants = this.getApplicants();
    const filteredApplicants = currentApplicants.filter(app => app.id !== applicantId);
    this.setApplicants(filteredApplicants);
  }

  // Step navigation methods
  navigateToStep(step: number): void {
    this.currentStepSubject.next(step);
  }

  getCurrentStep(): number {
    return this.currentStepSubject.value;
  }

  // Selected applicant for edit methods
  setSelectedApplicantForEdit(applicant: VisaApplicant | null): void {
    this.selectedApplicantForEditSubject.next(applicant);
  }

  getSelectedApplicantForEdit(): VisaApplicant | null {
    return this.selectedApplicantForEditSubject.value;
  }

  clearSelectedApplicantForEdit(): void {
    this.selectedApplicantForEditSubject.next(null);
  }

  // Utility methods
  private generateUniqueId(): string {
    return 'applicant_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  // Reset all data
  resetData(): void {
    this.selectedMarineMediumSubject.next(null);
    this.applicantsSubject.next([]);
    this.currentStepSubject.next(1);
    this.selectedApplicantForEditSubject.next(null);
  }

  // Validation methods
  isMarineMediumSelected(): boolean {
    return this.getSelectedMarineMedium() !== null;
  }

  hasApplicants(): boolean {
    return this.getApplicants().length > 0;
  }

  isReadyForPreview(): boolean {
    return this.isMarineMediumSelected() && this.hasApplicants();
  }
}
