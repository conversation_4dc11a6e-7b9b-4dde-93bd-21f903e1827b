<div class="logo-Ebhar-sub" *jhiHasAnyAuthority="['ROLE_SERVICE_PROVIDER', 'ROLE_INDIVIDUAL_USER']">
  <!-- <app-header /> -->
  <div class="mk-permit-container mk-inner-layout-container mk-create-request">
    <div class="p-3">
      <div class="mk-inner-layout-content">
        <div *ngIf="serviceDefinitionPage">
          <div class="row">
            <jhi-service-type-definition-card
              [selectedServiceType]="selectedServiceType"
              [targetAudience]="TARGET_AUDIENCE"
            ></jhi-service-type-definition-card>
            <div class="custom-card">
              <div class="col-lg-8 d-flex-between gap-3 mb-4">
                <div class="d-flex flex-column justify-content-start my-3 gap-3">
                  <app-page-title [title]="'srsaApp.visaTourism.home.title' | translate" />
                  <div class="d-grid">
                    <span class="linces-header-2">{{ 'srsaApp.visaTourism.home.title' | translate }}</span>
                  </div>
                </div>
                <div class="w-sm-100">
                  <button
                    type="button"
                    class="btn btn-primary px-4 px-xl-5 w-sm-100"
                    data-bs-toggle="modal"
                    data-bs-target="#terms-conditions-modal"
                    jhiTranslate="srsaApp.mta.startService"
                  >
                    بدء الخدمة
                  </button>
                </div>
              </div>
              <p class="">
                {{ isArabic() ? selectedServiceType?.descAr : selectedServiceType?.descEn }}
              </p>
            </div>
            <jhi-request-requirements
              [businessRoles]="businessRoles"
              [steps]="steps"
              [requiredDocuments]="requiredDocuments"
            ></jhi-request-requirements>
          </div>
        </div>
        <section *ngIf="wizardDisplay">
          <jhi-request-wizard-stepper [steps]="steps" [currentStep]="step"></jhi-request-wizard-stepper>
          <div *ngIf="step == STEP_SELECT_MARINE_MEDIUM">
            <jhi-form-step-select-marine-medium
              [needActiveNavPermit]="true"
              [needActiveTechPermit]="true"
              [needValidateVisaPassengersCount]="true"
              [marineMediums]="marineMediums"
              [(marineMedium)]="selectedMarineMedium"
              (marineMediumChange)="onMarineMediumSelected($event)"
            ></jhi-form-step-select-marine-medium>
          </div>
          <div *ngIf="step == STEP_VISA_FORM_ADD_APPLICANT">
            <form name="editForm" role="form" novalidate (ngSubmit)="submit()" [formGroup]="editForm">
              <jhi-visa-form-step-add-applicant></jhi-visa-form-step-add-applicant>
            </form>
          </div>
          <div *ngIf="step == STEP_SELECT_REQUEST_PREVIEW">
            <jhi-visa-form-step-request-preview></jhi-visa-form-step-request-preview>
          </div>

          <div class="mk-wizard-actions d-flex-between gap-3 mk-wizard-btns">
            <div class="d-flex-between gap-3">
              <a
                href="javascript:void(0)"
                (click)="prevStep()"
                *ngIf="step > STEP_SELECT_MARINE_MEDIUM"
                class="btn btn-outline-primary mk-btn-prev"
                jhiTranslate="global.menu.previous"
              >
                السابق
              </a>
              <a
                href="javascript:void(0)"
                (click)="nextStep()"
                *ngIf="step < STEP_SELECT_REQUEST_PREVIEW"
                class="btn btn-outline-primary mk-btn-next"
                jhiTranslate="global.menu.next"
              >
                التالي
              </a>
              <!--                [ngClass]="(currentStep === 1 && !showCrDetails) || isCurrentWizardInvalid() ? 'disabled' : ''"-->

              <div>
                <button
                  type="button"
                  data-cy="entityCreateCancelButton"
                  class="btn btn-primary"
                  data-bs-toggle="modal"
                  data-bs-target="#liabilityModal"
                  *ngIf="step == STEP_SELECT_REQUEST_PREVIEW"
                >
                  <span jhiTranslate="srsaApp.permitRequest.detail.sendRequest">إرسال الطلب</span>
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</div>
<jhi-terms-conditions-modal [termsConditionsKey]="'visa-tourism-request'" (accepted)="startService()"></jhi-terms-conditions-modal>
