import { Component, inject, input, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';

import SharedModule from 'app/shared/shared.module';
import { DurationPipe, FormatMediumDatetimePipe, FormatMediumDatePipe } from 'app/shared/date';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DropzoneConfigInterface, DropzoneModule } from 'ngx-dropzone-wrapper';
import { GregorianCalendarComponent } from '../../../shared/gregorian-calendar/gregorian-calendar.component';
import HasAnyAuthorityDirective from '../../../shared/auth/has-any-authority.directive';
import { TabViewModule } from 'primeng/tabview';
import { IPermitVisit } from '../../permit-request/permit-visit.model';
import { IPermitRequestStatus } from '../../permit-request-status/permit-request-status.model';
import { IPermitRequestHistory } from '../../permit-request-history/permit-request-history.model';
import { PermitRequestHistoryService } from '../../permit-request-history/service/permit-request-history.service';
import { PermitRequestStatusService } from '../../permit-request-status/service/permit-request-status.service';
import { AccountService } from '../../../core/auth/account.service';
import { TranslateService } from '@ngx-translate/core';
import { FileKeyValuePair } from '../../permit-request/update/permit-request-update.component';
import { Account } from '../../../core/auth/account.model';
import { StateStorageService } from '../../../core/auth/state-storage.service';
import { PaymentService, PayPermitRequestFeeEntityResponseType } from '../../payment/service/payment.service';
import { AlertService } from '../../../core/util/alert.service';
import {CommonModule, Location} from '@angular/common';
import { ICompanyOwner } from '../../../account/register/model/company.owner.model';
import { IMaritimeMedium } from '../../maritime-medium/maritime-medium.model';
import { map } from 'rxjs/operators';
import { HttpResponse } from '@angular/common/http';
import { IPayPermitRequestFeeRequest, PayPermitRequestFeeRequest } from '../../payment/pay-permit-request-fee-request.model';
import { StatusType } from '../../enumerations/status-type.model';
import { roleActionMap } from '../../permit-request/order-management/role.action.map';
import { DisplayFiles } from '../../permit-request/order-management/order-management.component';
import { Authority } from '../../../config/authority.constants';
import { PermitType } from '../../enumerations/permit-type.model';
import { MarineMedium } from '../../marine-medium/model/marine-medium.model';
import { MarineMediumService } from '../../marine-medium/marine-medium.service';
import { TechnicalPermitStatusService } from '../../technical-permit-status/service/technical-permit-status.service';
import { ITechnicalPermitStatus } from '../../technical-permit-status/technical-permit-status.model';
import { ITechPermitNationality } from '../../tech-permit-nationality/tech-permit-nationality.model';
import { TechPermitNationalityService } from '../../tech-permit-nationality/service/tech-permit-nationality.service';
import { EntityArrayResponseType } from '../../mta/service/mta.service';
import { PermitActivity } from '../../enumerations/permit-activity.model';
import { TechnicalPermitActivityService } from '../../technical-permit-activity/service/technical-permit-activity.service';
import { IMarineAccessoriesItem } from '../../marine-accessories-item/marine-accessories-item.model';
import { MarineAccessoriesItemService } from '../../marine-accessories-item/service/marine-accessories-item.service';
import { AppDialogService } from 'app/layouts/payment-dialog/dialog.service';
import { DynamicDialogModule } from 'primeng/dynamicdialog';
import { PermitPurpose } from '../../enumerations/permit-purpose.model';
import { RadioButtonModule } from 'primeng/radiobutton';
import {IAccessory} from "../../technical-permit/accessory.model";
import {TechnicalPermitService} from "../../technical-permit/service/technical-permit.service";
import {NewTechPermitActivity} from "../../technical-permit/tech-permit-activity.model";
import {IVisaTourismRequestModel} from "../visa-tourism-request.model";
import {VisaRequestService} from "../visa-tourism-request.service";
declare var $: any;

@Component({
  selector: 'jhi-visa-request-view',
  standalone: true,
  templateUrl: './visa-request-view.component.html',
  styleUrl: './visa-request-view.component.scss',
  imports: [
    SharedModule,
    RouterModule,
    FormatMediumDatetimePipe,
    ReactiveFormsModule,
    FormsModule,
    DropzoneModule,
    HasAnyAuthorityDirective,
    TabViewModule,
    RadioButtonModule,
  ],
  providers: [DynamicDialogModule],
})
export class VisaRequestViewComponent implements OnInit {
  validPermit = true;
  validPermitStartDate: any;
  validPermitEndDate: any;
  validPermitIndex = 0;
  saudiMarineMedium = false;
  selectedMarineMedium: MarineMedium | null = null;
  selectedReturnOption: string = 'REQUEST_PROVIDER';
  requestNote = '';
  visaTourismRequest = input<IVisaTourismRequestModel | null>(null);
  public router = inject(Router);
  public stateStorageService = inject(StateStorageService);
  public paymentService = inject(PaymentService);
  public alertService = inject(AlertService);
  public location = inject(Location);
  protected marineMediumService = inject(MarineMediumService);
  ownerIsIndividual = false;
  ownerIsServiceProvider = false;

  ngOnInit() {
    // @ts-ignore
    this.visaRequestService.find(this.visaTourismRequest()!.id).subscribe({
      next: response => {
        // @ts-ignore
        this.visaTourismRequest= response.body;
      },
      error: err => {},
    });
    this.accountService.identity().subscribe(
      account => {
        this.account = account;
        // Any additional code that depends on account can go here
      },
      error => {
        console.error('Error loading account:', error);
      },
    );
  }

  previousState(): void {
    this.router.navigate(['/visa-tourism']);
  }

  step1: boolean = true;
  step2: boolean = false;
  step3: boolean = false;
  // permitRequest: ITechnicalPermit | null = null;
  licensingOfficers = [];
  hasAction = false;
  licensingManager = [];
  allPermitVisits: IPermitVisit[] = [];
  completedPermitVisits: IPermitVisit[] = [];
  pendingPermitVisits: IPermitVisit[] = [];
  technicalPermitStatus: ITechnicalPermitStatus[] = [];
  permitRequestHistory: IPermitRequestHistory[] = [];
  techPermitNationalitiesSharedCollection: ITechPermitNationality[] = [];
  selectedTechPermitActivities?: PermitActivity[] | any = [];
  accessories: IAccessory[] = [];
  protected technicalPermitActivityService = inject(TechnicalPermitActivityService);
  protected marineAccessoriesItemService = inject(MarineAccessoriesItemService);
  inspectionOfficers = [];
  criteria: any;
  permitRequestHistoryService = inject(PermitRequestHistoryService);
  technicalPermitStatusService = inject(TechnicalPermitStatusService);
  protected techPermitNationalityService = inject(TechPermitNationalityService);

  // permitRequestService = inject(PermitRequestService);
  visaRequestService = inject(VisaRequestService);
  activatedRoute = inject(ActivatedRoute);
  accountService = inject(AccountService);
  translateService = inject(TranslateService);
  selectedOfficer: any;
  selectedInspectionOfficer: any;
  selectedLicenseManager: any;
  permitChanges: any = {};
  targetPage = '';
  documentsDisplay: DisplayFiles[] = [] as DisplayFiles[];
  documents: FileKeyValuePair[] = [] as FileKeyValuePair[];
  documentTypes: any[] = [];
  visitReportDocuments: FileKeyValuePair[] = [] as FileKeyValuePair[];
  fileSizeError = false;
  entityForm = new FormGroup({
    visitDate: new FormControl(),
  });
  fileConfig: DropzoneConfigInterface = {
    maxFilesize: 5,
    clickable: true,
    addRemoveLinks: true,
    maxFiles: 1,
    acceptedFiles: '.pdf,.jpeg,.jpg,.png',
    dictDefaultMessage: this.isArabic() ? 'اضغط هنا لرفع الملف' : 'Press here to upload file',
    dictInvalidFileType: this.isArabic()
      ? 'لا تستطيع رفع ملف من هذه الصيغة. (الصيغ المسموحة: pdf, jpeg ,jpg ,png)'
      : 'Cannot upload this file. (Allowed extensions: pdf, jpeg ,jpg ,png)',
    dictFileTooBig: this.isArabic() ? 'الحجم الأقصى للصورة هو 5 MB' : 'Maximum image size is 5 MB',
    url: SERVER_API_URL + '/api/files',
    //   headers: {'X-XSRF-TOKEN': this.cookieService.get('XSRF-TOKEN')}
  };
  needVisit: string = 'inspectionDepartment';
  visitedBefore: string = 'No';
  visitDate: any;
  baseUrl = '../../../../content/images';
  menuItems: any[] = [
    {
      title: 'انشاء الطلب',
      paragraph: 'تاريخ إنشاء الطلب',
      date: '14/02/2024  |  01:00 AM',
      icon: `${this.baseUrl}/plus.png`,
    },
    {
      title: 'بانتظار المراجعة',
      paragraph: 'بانتظار المراجعة من مشرف التراخيص',
      date: '',
      icon: `${this.baseUrl}/wallet.png`,
    },
    {
      title: 'تحت المراجعة',
      paragraph: 'بانتظار المراجعة من مسؤول التراخيص',
      date: '',
      icon: `${this.baseUrl}/wallet.png`,
    },
    {
      title: 'انتظار التفتيش',
      paragraph: 'بانتظار المراجعة من مشرف التفتيش',
      date: '',
      icon: `${this.baseUrl}/search.png`,
    },
    {
      title: 'تحت التفتيش',
      paragraph: 'بانتظار المراجعة من مسؤول التفتيش',
      date: '',
      icon: `${this.baseUrl}/search.png`,
    },
    {
      title: ' تحت موافقة تقرير التفتيش',
      paragraph: 'بانتظار المراجعة من مسؤول التفتيش',
      date: '',
      icon: `${this.baseUrl}/search.png`,
    },
    {
      title: 'تحت الموافقة النهائية',
      paragraph: 'بانتظار المراجعة من مسؤول التراخيص',
      date: '',
      icon: `${this.baseUrl}/wallet.png`,
    },
  ];
  account: Account | null = null;

  hasPaymentRequest: boolean = false;
  loading: boolean = false;
  fail: boolean = false;
  isLoading: boolean = false;
  success: boolean = false;
  availableActions: string[] = [];

  licenceOfficerConfirmModal: boolean = false;

  public appDialog = inject(AppDialogService);

  isArabic() {
    let currentLanguage = this.translateService.currentLang;
    currentLanguage = currentLanguage === '' ? 'ar' : currentLanguage;
    return currentLanguage.startsWith('ar', 0);
  }
  trackIdentity(_index: number, item: ICompanyOwner): string {
    return item.name;
  }
  trackMaritimeMedium(_index: number, item: IMaritimeMedium): string {
    return item.code!;
  }
  getShipPermitInfo() {
    if (this.visaTourismRequest()!.marineMedium!.id)
      this.marineMediumService.getShipInfo(this.visaTourismRequest()!.marineMedium!.id).subscribe({
        next: response => {
          this.selectedMarineMedium = response.data as MarineMedium;
          if (
            this.selectedMarineMedium.permitSaudiShipResponse != null &&
            this.selectedMarineMedium.permitSaudiShipResponse.isSuccess === true &&
            this.selectedMarineMedium.permitSaudiShipResponse.data != null
          ) {
            this.validPermitIndex = 0;
            for (let index = 0; index < this.selectedMarineMedium!.permitSaudiShipResponse!.data!.length; index++) {
              const shipDataRs = this.selectedMarineMedium!.permitSaudiShipResponse!.data[index];
              if (shipDataRs.permitEndDate && new Date(shipDataRs.permitEndDate).getTime() > new Date().getTime()) {
                this.validPermit = true;
                this.validPermitStartDate = shipDataRs.permitStartDate;
                this.validPermitEndDate = shipDataRs.permitEndDate;
                this.saudiMarineMedium = true;
                this.validPermitIndex = index; // Set the index of the current element
                break;
              }
            }
          }

          if (
            this.selectedMarineMedium.permitForeignShipResponse != null &&
            this.selectedMarineMedium.permitForeignShipResponse.isSuccess === true &&
            this.selectedMarineMedium.permitForeignShipResponse.data != null
          ) {
            this.validPermitIndex = 0;
            for (let index = 0; index < this.selectedMarineMedium!.permitForeignShipResponse!.data.length; index++) {
              const shipDataRs = this.selectedMarineMedium.permitForeignShipResponse.data[index];
              if (shipDataRs.permitEndDate && new Date(shipDataRs.permitEndDate).getTime() > new Date().getTime()) {
                this.validPermit = true;
                this.validPermitStartDate = shipDataRs.permitStartDate;
                this.validPermitEndDate = shipDataRs.permitEndDate;
                this.saudiMarineMedium = false;
                this.validPermitIndex = index; // Set the index of the current element
                break;
              }
            }
          }
        },
        error: err => {},
      });
  }



  cancel() {
    this.location.back(); // Use Location service to navigate back
  }


  // sendToPayPermitRequest() {
  //   this.visaTourismRequest()!.requestNote = this.requestNote;
  //   this.techPermitService.sendToPayTechnicalPermit(this.visaTourismRequest(), this.documents).subscribe(res => {
  //     this.location.back(); // Use Location service to navigate back
  //   });
  // }

  getTodayDate() {
    return $.calendars.instance().today().formatDate('yyyy-mm-dd');
  }

  pay(): void {
    this.isLoading = true;
    this.fail = false;
    const payPermitRequestFeeRequest: IPayPermitRequestFeeRequest = new PayPermitRequestFeeRequest(
      this.visaTourismRequest()?.id,
      this.isArabic() ? 'ar' : 'en',
      PermitType.TOURISM_VISA,
    );

    this.paymentService.payPermitRequestFee(payPermitRequestFeeRequest).subscribe({
      next: (res: PayPermitRequestFeeEntityResponseType) => {
        if (res.body?.success === true) {
          window.open(res.body.paymentLink, '_self');
          this.isLoading = false;
          this.fail = false;
          this.success = true;
        } else {
          this.isLoading = false;
          this.fail = true;
          this.alertService.addAlert({ type: 'danger', message: res.body?.message, translationKey: res.body?.message });
        }
      },
      error: (err: any) => {
        this.isLoading = false;
        this.fail = true;
      },
      complete() {},
    });
  }


  exportPDF(): void {
    this.visaRequestService.exportPdf(this.visaTourismRequest()!.id).subscribe({
      next: (data: ArrayBuffer) => {
        const blob = new Blob([data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        window.open(url, '_blank');
      },
      error(err: any) {
        console.error('Error generating PDF:', err);
      },
      complete() {},
    });
  }
  printInvoicePdf(): void {
    this.loading = true;
    this.fail = false;
    this.paymentService.downloadInvoicePdfTechPermit(this.visaTourismRequest()!.id).subscribe({
      next: (data: ArrayBuffer) => {
        const blob = new Blob([data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        window.open(url, '_blank');
        this.loading = false;
        this.fail = false;
      },
      error: (err: any) => {
        this.loading = false;
        this.fail = true;
        console.error('Error generating PDF:', err);
      },
      complete() {},
    });
  }


  confirm(event: boolean): void {
    this.licenceOfficerConfirmModal = event;
  }

  getNatinoality(natinoalityId: any) {
    return this.isArabic()
      ? this.techPermitNationalitiesSharedCollection?.find(x => x.id == natinoalityId)?.nameAr
      : this.techPermitNationalitiesSharedCollection?.find(x => x.id == natinoalityId)?.nameEn;
  }

  protected readonly Authority = Authority;

  showDialog(): void {
    this.pay();
    // const title = this.translateService.instant('payment.chooseMethod');
    // const ref = this.appDialog.openDialog({
    //   title: title,
    // });
    //
    // ref.onClose.subscribe((chosenMethod: string) => {
    //   if (chosenMethod === 'credit_card') {
    //     this.pay();
    //   } else {
    //     this.sadad();
    //   }
    // });
  }

  sadad(): void {}
}
