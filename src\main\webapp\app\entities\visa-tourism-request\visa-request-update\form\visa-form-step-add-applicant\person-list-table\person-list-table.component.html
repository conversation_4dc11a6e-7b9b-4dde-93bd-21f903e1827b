<div class="person-list-table-container">
  <!-- Table Header -->
  <div class="table-header mb-3">
    <h6 class="table-title mb-0" jhiTranslate="srsaApp.visaTourism.visaModal.personList.title">Added Persons</h6>
    <span class="person-count badge bg-primary">{{ persons.length }}</span>
  </div>

  <!-- Empty State -->
  <div *ngIf="persons.length === 0" class="text-center py-4">
    <i class="fas fa-users fa-3x text-muted mb-3"></i>
    <p class="text-muted" jhiTranslate="srsaApp.visaTourism.visaModal.personList.emptyState">No persons added yet</p>
    <small class="text-muted" jhiTranslate="srsaApp.visaTourism.visaModal.personList.emptyStateDescription">
      Click "Add Person" to add travelers to this visa application
    </small>
  </div>

  <!-- Data Table -->
  <div *ngIf="persons.length > 0" class="table-responsive">
    <table class="table table-striped mk-table">
      <thead>
        <tr>
          <th scope="col">
            <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.mobileNumber">Mobile Number</span>
          </th>
          <th scope="col">
            <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.passportNumber">Passport Number</span>
          </th>
          <th scope="col">
            <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.name">Name</span>
          </th>
          <th scope="col">
            <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.nationality">Nationality</span>
          </th>
          <th scope="col">
            <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.gender">Gender</span>
          </th>
          <th scope="col" class="text-center">
            <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.actions">Actions</span>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let person of persons; let i = index">
          <td>
            <span class="table-cell-title d-none">{{ 'srsaApp.visaTourism.visaModal.personList.mobileNumber' | translate }}</span>
            {{ getDisplayPhoneNumber(person) }}
          </td>
          <td>
            <span class="table-cell-title d-none">{{ 'srsaApp.visaTourism.visaModal.personList.passportNumber' | translate }}</span>
            {{ getDisplayPassportNumber(person) }}
          </td>
          <td>
            <span class="table-cell-title d-none">{{ 'srsaApp.visaTourism.visaModal.personList.name' | translate }}</span>
            {{ getDisplayName(person) }}
          </td>
          <td>
            <span class="table-cell-title d-none">{{ 'srsaApp.visaTourism.visaModal.personList.nationality' | translate }}</span>
            {{ getDisplayNationality(person) }}
          </td>
          <td>
            <span class="table-cell-title d-none">{{ 'srsaApp.visaTourism.visaModal.personList.gender' | translate }}</span>
            <span *ngIf="person.gender === 'MALE'" jhiTranslate="srsaApp.visaTourism.visaModal.male">Male</span>
            <span *ngIf="person.gender === 'FEMALE'" jhiTranslate="srsaApp.visaTourism.visaModal.female">Female</span>
            <span *ngIf="!person.gender" class="text-muted">-</span>
          </td>
          <td class="text-center">
            <span class="table-cell-title d-none">{{ 'srsaApp.visaTourism.visaModal.personList.actions' | translate }}</span>
            <div class="btn-group btn-group-sm" role="group">
              <!-- <button
                type="button"
                class="btn btn-outline-primary btn-sm me-1"
                (click)="onEditPerson(person)"
                title="{{ 'srsaApp.visaTourism.visaModal.personList.edit' | translate }}"
              >
                <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.edit">Edit</span>
              </button> -->
              <button
                type="button"
                class="btn btn-outline-danger btn-sm"
                (click)="confirmRemovePerson(person)"
                title="{{ 'srsaApp.visaTourism.visaModal.personList.remove' | translate }}"
              >
                <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.remove">Remove</span>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Table Footer -->
  <div *ngIf="persons.length > 0" class="table-footer mt-3">
    <div class="row align-items-center">
      <div class="col-md-6">
        <small class="text-muted">
          <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.totalPersons">Total Persons:</span>
          <strong>{{ persons.length }}</strong>
        </small>
      </div>
    </div>
  </div>
</div>
