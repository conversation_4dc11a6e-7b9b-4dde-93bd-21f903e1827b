<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2 class="mb-4" jhiTranslate="srsaApp.visaTourism.preview.title">Request Preview</h2>

      <!-- Review Accordion -->
      <div class="accordion mk-review-step" id="visaPreviewAccordion">
        <!-- Maritime Medium Details Section -->
        <div class="accordion-item mb-4 rounded-4 overflow-hidden">
          <h2 class="accordion-header d-flex-center">
            <img src="../../../../content/images/request-view/ph_building-bold.svg" alt="maritime-medium-icon" class="mx-4 me-3" />
            <button
              class="accordion-button"
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#collapseMarineMedium"
              aria-expanded="true"
              aria-controls="collapseMarineMedium"
              jhiTranslate="srsaApp.visaTourism.preview.marineMediumDetails"
            >
              <span jhiTranslate="srsaApp.visaTourism.preview.marineMediumDetails">Maritime Medium Details</span>
            </button>
          </h2>
          <div id="collapseMarineMedium" class="accordion-collapse collapse show" data-bs-parent="#visaPreviewAccordion">
            <div class="accordion-body mx-2rem">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0" jhiTranslate="srsaApp.visaTourism.preview.selectedVessel">Selected Vessel</h5>
                <button
                  type="button"
                  class="btn btn-outline-primary btn-sm"
                  (click)="onEditMarineMedium()"
                  jhiTranslate="global.entity.action.edit"
                >
                  Edit
                </button>
              </div>

              <div *ngIf="selectedMarineMedium; else noMarineMedium">
                <div class="row g-3">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label fw-bold" jhiTranslate="srsaApp.marineMedium.name">Name</label>
                      <p class="mb-0">{{ getMarineMediumName() }}</p>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label fw-bold" jhiTranslate="srsaApp.marineMedium.type">Type</label>
                      <p class="mb-0">{{ getMarineMediumType() }}</p>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label fw-bold" jhiTranslate="srsaApp.marineMedium.registrationNumber">Registration Number</label>
                      <p class="mb-0">{{ selectedMarineMedium.registrationNumber || 'N/A' }}</p>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label fw-bold" jhiTranslate="srsaApp.marineMedium.imoNumber">IMO Number</label>
                      <p class="mb-0">{{ selectedMarineMedium.imoNumber || 'N/A' }}</p>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label fw-bold" jhiTranslate="srsaApp.marineMedium.flagNationality">Flag Nationality</label>
                      <p class="mb-0">{{ getFlagNationality() }}</p>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <label class="form-label fw-bold" jhiTranslate="srsaApp.marineMedium.passengersCount">Passengers Count</label>
                      <p class="mb-0">{{ selectedMarineMedium.passengersCount || 'N/A' }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <ng-template #noMarineMedium>
                <div class="alert alert-warning" role="alert">
                  <i class="fas fa-exclamation-triangle me-2"></i>
                  <span jhiTranslate="srsaApp.visaTourism.preview.noMarineMediumSelected">No maritime medium selected</span>
                </div>
              </ng-template>
            </div>
          </div>
        </div>

        <!-- Applicants Table Section -->
        <div class="accordion-item mb-4 rounded-4 overflow-hidden">
          <h2 class="accordion-header d-flex-center">
            <img src="../../../../content/images/request-view/ic_round-add-card.svg" alt="applicants-icon" class="mx-4 me-3" />
            <button
              class="accordion-button"
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#collapseApplicants"
              aria-expanded="true"
              aria-controls="collapseApplicants"
              jhiTranslate="srsaApp.visaTourism.preview.applicantsDetails"
            >
              <span jhiTranslate="srsaApp.visaTourism.preview.applicantsDetails">Visa Applicants</span>
            </button>
          </h2>
          <div id="collapseApplicants" class="accordion-collapse collapse show" data-bs-parent="#visaPreviewAccordion">
            <div class="accordion-body mx-2rem">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                  <span jhiTranslate="srsaApp.visaTourism.preview.applicantsList">Applicants List</span>
                  <span class="badge bg-primary ms-2">{{ applicants.length }}</span>
                </h5>
                <button
                  type="button"
                  class="btn btn-outline-primary btn-sm"
                  (click)="onEditApplicants()"
                  jhiTranslate="global.entity.action.edit"
                >
                  Edit
                </button>
              </div>

              <div *ngIf="applicants.length > 0; else noApplicants">
                <!-- Applicants Table -->
                <div class="table-responsive">
                  <table class="table table-striped mk-table">
                    <thead>
                      <tr>
                        <th scope="col">
                          <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.name">Name</span>
                        </th>
                        <th scope="col">
                          <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.passportNumber">Passport Number</span>
                        </th>
                        <th scope="col">
                          <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.nationality">Nationality</span>
                        </th>
                        <th scope="col">
                          <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.gender">Gender</span>
                        </th>
                        <th scope="col">
                          <span jhiTranslate="srsaApp.visaTourism.mobileNumber">Mobile Number</span>
                        </th>
                        <th scope="col">
                          <span jhiTranslate="srsaApp.visaTourism.emailAddress">Email Address</span>
                        </th>
                        <th scope="col" class="text-center">
                          <span jhiTranslate="srsaApp.visaTourism.visaModal.personList.actions">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let applicant of applicants; let i = index">
                        <td>
                          <span class="table-cell-title d-none">{{ 'srsaApp.visaTourism.visaModal.personList.name' | translate }}</span>
                          {{ applicant.fullName || 'N/A' }}
                        </td>
                        <td>
                          <span class="table-cell-title d-none">{{
                            'srsaApp.visaTourism.visaModal.personList.passportNumber' | translate
                          }}</span>
                          {{ applicant.passportNumber || 'N/A' }}
                        </td>
                        <td>
                          <span class="table-cell-title d-none">{{
                            'srsaApp.visaTourism.visaModal.personList.nationality' | translate
                          }}</span>
                          {{ applicant.nationality || 'N/A' }}
                        </td>
                        <td>
                          <span class="table-cell-title d-none">{{ 'srsaApp.visaTourism.visaModal.personList.gender' | translate }}</span>
                          {{ applicant.gender || 'N/A' }}
                        </td>
                        <td>
                          <span class="table-cell-title d-none">{{ 'srsaApp.visaTourism.mobileNumber' | translate }}</span>
                          {{ applicant.phoneNumber || 'N/A' }}
                        </td>
                        <td>
                          <span class="table-cell-title d-none">{{ 'srsaApp.visaTourism.emailAddress' | translate }}</span>
                          {{ applicant.email || 'N/A' }}
                        </td>
                        <td class="text-center">
                          <div class="btn-group" role="group">
                            <button
                              type="button"
                              class="btn btn-outline-danger btn-sm"
                              (click)="onRemoveApplicant(applicant)"
                              [title]="'global.entity.action.delete' | translate"
                            >
                              <i class="fas fa-trash"></i>
                              <span class="d-none d-md-inline" jhiTranslate="global.entity.action.delete">Delete</span>
                            </button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <ng-template #noApplicants>
                <div class="alert alert-warning" role="alert">
                  <i class="fas fa-exclamation-triangle me-2"></i>
                  <span jhiTranslate="srsaApp.visaTourism.preview.noApplicantsAdded">No applicants added yet</span>
                </div>
              </ng-template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
