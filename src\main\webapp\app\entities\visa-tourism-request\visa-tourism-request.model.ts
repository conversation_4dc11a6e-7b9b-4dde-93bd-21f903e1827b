import { VisaApplicant } from './visa-applicant.model';
import { VisaRequestStatusEnum } from './visa-request-status.enum';
import dayjs from 'dayjs/esm';
import {MarineMedium} from "../marine-medium/model/marine-medium.model";
import {IYachtCharterPermit} from "../yacht-charter-permit/yacht-charter-permit.model";

export interface IVisaTourismRequestModel {
  id?: number;
  marinMediumId?: number;
  requestStatus?: keyof typeof VisaRequestStatusEnum;
  requestNumber?: string;
  visaType: string;
  ownerId: number;
  applicants: VisaApplicant[];
  fees?: number | null;
  submitDate?: dayjs.Dayjs | null;

  marineMedium?: MarineMedium | null | undefined;
}
export type NewVisaTourismRequestModel = Omit<IVisaTourismRequestModel, 'id'> & { id: null };
