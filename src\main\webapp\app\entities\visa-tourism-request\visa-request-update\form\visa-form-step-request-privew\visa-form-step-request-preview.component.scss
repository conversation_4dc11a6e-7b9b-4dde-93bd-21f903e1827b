// Preview component specific styles
.preview-section {
  margin-bottom: 2rem;
}

.preview-header {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.preview-field {
  margin-bottom: 1rem;
}

.preview-field label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
}

.preview-field p {
  color: #6c757d;
  margin-bottom: 0;
}

.edit-button {
  font-size: 0.875rem;
}

// Responsive table improvements
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }

  .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

// Alert styling
.alert {
  border-radius: 0.5rem;
}

.alert-warning {
  background-color: #fff3cd;
  border-color: #ffecb5;
  color: #856404;
}

// Badge styling
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}
